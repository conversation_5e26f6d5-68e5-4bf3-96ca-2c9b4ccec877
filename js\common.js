/**
 * StudyNotes Enhanced JavaScript Architecture
 * Modern ES6+ implementation with improved error handling and performance
 */

// StudyNotes Application Namespace - Enhanced but Compatible
const StudyNotes = window.StudyNotes || {
    // Configuration
    config: {
        apiTimeout: 30000,
        debounceDelay: 300,
        animationDuration: 300,
        retryAttempts: 3,
        enableAdvancedFeatures: false // Disabled by default for compatibility
    },

    // State management
    state: {
        isLoading: false,
        activeModals: new Set(),
        formData: new Map(),
        eventListeners: new Map(),
        initialized: false
    },

    // Utility functions
    utils: {},

    // Component modules
    components: {},

    // API handling
    api: {},

    // Event system
    events: {},

    // Initialize application
    init() {
        if (this.state.initialized) return; // Prevent double initialization

        try {
            this.utils.init();
            this.api.init();
            this.events.init();
            this.components.init();

            // Only initialize advanced features if enabled
            if (this.config.enableAdvancedFeatures) {
                this.ui && this.ui.init();
                this.cursor && this.cursor.init();
                this.theme && this.theme.init();
                this.animations && this.animations.init();
                this.dragDrop && this.dragDrop.init();
                this.search && this.search.init();
                this.performance && this.performance.init();
                console.log('StudyNotes application initialized with advanced features');
            } else {
                console.log('StudyNotes application initialized in compatibility mode');
            }

            this.state.initialized = true;
        } catch (error) {
            console.error('StudyNotes initialization error:', error);
        }
    },

    // Enable advanced features
    enableAdvancedFeatures() {
        this.config.enableAdvancedFeatures = true;
        if (this.state.initialized) {
            // Initialize advanced features if app is already initialized
            this.ui && this.ui.init();
            this.cursor && this.cursor.init();
            this.theme && this.theme.init();
            this.animations && this.animations.init();
            this.dragDrop && this.dragDrop.init();
            this.search && this.search.init();
            this.performance && this.performance.init();

            // Enable custom cursor
            document.body.classList.add('custom-cursor-enabled');
            console.log('Advanced features enabled');
        }
    }
};

// Enhanced Utility Functions
StudyNotes.utils = {
    init() {
        this.setupGlobalErrorHandling();
        this.setupPerformanceMonitoring();
    },

    // Debounce function for performance optimization
    debounce(func, wait = StudyNotes.config.debounceDelay) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function for scroll/resize events
    throttle(func, limit = 100) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Enhanced DOM element selection with error handling
    $(selector, context = document) {
        try {
            const elements = context.querySelectorAll(selector);
            return elements.length === 1 ? elements[0] : elements;
        } catch (error) {
            console.error(`Invalid selector: ${selector}`, error);
            return null;
        }
    },

    // Safe element creation
    createElement(tag, attributes = {}, content = '') {
        try {
            const element = document.createElement(tag);

            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'dataset') {
                    Object.entries(value).forEach(([dataKey, dataValue]) => {
                        element.dataset[dataKey] = dataValue;
                    });
                } else {
                    element.setAttribute(key, value);
                }
            });

            if (content) {
                element.innerHTML = content;
            }

            return element;
        } catch (error) {
            console.error('Error creating element:', error);
            return null;
        }
    },

    // Enhanced form validation
    validateForm(form) {
        if (!form) return { isValid: false, errors: ['Form not found'] };

        const errors = [];
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                errors.push(`${field.name || field.id || 'Field'} is required`);
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                errors.push('Please enter a valid email address');
                field.classList.add('is-invalid');
            }
        });

        return { isValid: errors.length === 0, errors };
    },

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Local storage with error handling
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Error saving to localStorage:', error);
                return false;
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Error reading from localStorage:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Error removing from localStorage:', error);
                return false;
            }
        }
    },

    // Global error handling
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showNotification('An unexpected error occurred', 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showNotification('An unexpected error occurred', 'error');
        });
    },

    // Performance monitoring
    setupPerformanceMonitoring() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
        }
    },

    // Enhanced notification system
    showNotification(message, type = 'info', duration = 5000) {
        const notification = this.createElement('div', {
            className: `alert alert-${type} notification-toast`,
            style: 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; opacity: 0; transform: translateX(100%); transition: all 0.3s ease;'
        }, message);

        document.body.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });

        // Auto remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);

        return notification;
    }
};

// Enhanced API System
StudyNotes.api = {
    init() {
        this.setupInterceptors();
    },

    // Setup request/response interceptors
    setupInterceptors() {
        // Add loading state management
        this.originalFetch = window.fetch;
        window.fetch = (...args) => {
            StudyNotes.state.isLoading = true;
            return this.originalFetch(...args)
                .finally(() => {
                    StudyNotes.state.isLoading = false;
                });
        };
    },

    // Enhanced fetch with retry logic and better error handling
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: StudyNotes.config.apiTimeout
        };

        const config = { ...defaultOptions, ...options };

        // Add timeout support
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);
        config.signal = controller.signal;

        let lastError;

        for (let attempt = 1; attempt <= StudyNotes.config.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, config);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                } else {
                    return await response.text();
                }

            } catch (error) {
                lastError = error;
                console.warn(`API request attempt ${attempt} failed:`, error.message);

                if (attempt < StudyNotes.config.retryAttempts && !error.name === 'AbortError') {
                    await this.delay(1000 * attempt); // Exponential backoff
                    continue;
                }

                break;
            }
        }

        clearTimeout(timeoutId);
        throw lastError;
    },

    // Utility delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // Convenience methods
    get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    },

    post(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    put(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
};

// Enhanced Component System
StudyNotes.components = {
    init() {
        this.modal.init();
        this.form.init();
        this.table.init();
        this.navigation.init();
    },

    // Enhanced Modal Component
    modal: {
        init() {
            this.setupEventListeners();
        },

        setupEventListeners() {
            // Close modal on backdrop click
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    this.close(e.target);
                }
            });

            // Close modal on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && StudyNotes.state.activeModals.size > 0) {
                    const modals = Array.from(StudyNotes.state.activeModals);
                    this.close(modals[modals.length - 1]);
                }
            });

            // Setup close buttons
            document.addEventListener('click', (e) => {
                if (e.target.matches('.modal-close, .btn-cancel')) {
                    const modal = e.target.closest('.modal');
                    if (modal) this.close(modal);
                }
            });
        },

        open(modalId) {
            const modal = StudyNotes.utils.$(modalId);
            if (!modal) {
                console.error(`Modal ${modalId} not found`);
                return;
            }

            modal.style.display = 'block';
            StudyNotes.state.activeModals.add(modal);
            document.body.style.overflow = 'hidden';

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Animate in
            requestAnimationFrame(() => {
                modal.style.opacity = '1';
            });
        },

        close(modal) {
            if (typeof modal === 'string') {
                modal = StudyNotes.utils.$(modal);
            }

            if (!modal) return;

            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
                StudyNotes.state.activeModals.delete(modal);

                if (StudyNotes.state.activeModals.size === 0) {
                    document.body.style.overflow = '';
                }
            }, StudyNotes.config.animationDuration);

            // Reset forms in modal
            const forms = modal.querySelectorAll('form');
            forms.forEach(form => form.reset());
        }
    },

    // Enhanced Form Component
    form: {
        init() {
            this.setupAutoSave();
            this.setupValidation();
        },

        setupAutoSave() {
            const debouncedSave = StudyNotes.utils.debounce((form) => {
                const formId = form.id || `form_${Date.now()}`;
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                StudyNotes.utils.storage.set(`autosave_${formId}`, data);
            });

            document.addEventListener('input', (e) => {
                const form = e.target.closest('form');
                if (form && !form.classList.contains('no-autosave')) {
                    debouncedSave(form);
                }
            });
        },

        setupValidation() {
            document.addEventListener('submit', (e) => {
                const form = e.target;
                if (form.tagName === 'FORM') {
                    const validation = StudyNotes.utils.validateForm(form);
                    if (!validation.isValid) {
                        e.preventDefault();
                        StudyNotes.utils.showNotification(
                            validation.errors.join('<br>'),
                            'danger'
                        );
                    }
                }
            });
        },

        restoreAutoSave(formId) {
            const form = StudyNotes.utils.$(`#${formId}`);
            if (!form) return;

            const savedData = StudyNotes.utils.storage.get(`autosave_${formId}`);
            if (savedData) {
                Object.entries(savedData).forEach(([name, value]) => {
                    const field = form.querySelector(`[name="${name}"]`);
                    if (field) field.value = value;
                });
            }
        }
    },

    // Enhanced Table Component
    table: {
        init() {
            this.setupSorting();
            this.setupFiltering();
        },

        setupSorting() {
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-sort]')) {
                    const column = e.target.dataset.sort;
                    const table = e.target.closest('table');
                    this.sortTable(table, column);
                }
            });
        },

        setupFiltering() {
            const debouncedFilter = StudyNotes.utils.debounce((input) => {
                const table = document.querySelector(input.dataset.filterTable);
                if (table) {
                    this.filterTable(table, input.value);
                }
            });

            document.addEventListener('input', (e) => {
                if (e.target.matches('[data-filter-table]')) {
                    debouncedFilter(e.target);
                }
            });
        },

        sortTable(table, column) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            const isAscending = !table.dataset.sortDirection || table.dataset.sortDirection === 'desc';
            table.dataset.sortDirection = isAscending ? 'asc' : 'desc';

            rows.sort((a, b) => {
                const aVal = a.querySelector(`[data-${column}]`)?.textContent || '';
                const bVal = b.querySelector(`[data-${column}]`)?.textContent || '';

                const comparison = aVal.localeCompare(bVal, undefined, { numeric: true });
                return isAscending ? comparison : -comparison;
            });

            rows.forEach(row => tbody.appendChild(row));
        },

        filterTable(table, searchTerm) {
            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const matches = text.includes(searchTerm.toLowerCase());
                row.style.display = matches ? '' : 'none';
            });
        }
    },

    // Enhanced Navigation Component
    navigation: {
        init() {
            this.setupActiveStates();
            this.setupMobileMenu();
        },

        setupActiveStates() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar-menu a, .nav-menu a');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    link.classList.add('active');
                }
            });
        },

        setupMobileMenu() {
            const toggleBtn = document.querySelector('.mobile-menu-toggle');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn && sidebar) {
                toggleBtn.addEventListener('click', () => {
                    sidebar.classList.toggle('mobile-open');
                });

                // Close on outside click
                document.addEventListener('click', (e) => {
                    if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                        sidebar.classList.remove('mobile-open');
                    }
                });
            }
        }
    }
};

// Advanced UI System
StudyNotes.ui = {
    init() {
        this.setupScrollAnimations();
        this.setupParallaxEffects();
        this.setupMagneticElements();
        this.setupPageTransitions();
    },

    // Scroll-triggered animations
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },

    // Parallax scrolling effects
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.parallax');

        if (parallaxElements.length === 0) return;

        const handleScroll = StudyNotes.utils.throttle(() => {
            const scrolled = window.pageYOffset;

            parallaxElements.forEach(element => {
                const rate = scrolled * -0.5;
                element.style.transform = `translateY(${rate}px)`;
            });
        }, 16); // 60fps

        window.addEventListener('scroll', handleScroll);
    },

    // Magnetic hover effects
    setupMagneticElements() {
        const magneticElements = document.querySelectorAll('.magnetic');

        magneticElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;

                element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
            });

            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translate(0, 0)';
            });
        });
    },

    // Page transition system
    setupPageTransitions() {
        const transitionElement = document.createElement('div');
        transitionElement.className = 'page-transition';
        document.body.appendChild(transitionElement);

        // Intercept navigation
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && !link.hasAttribute('target') && link.hostname === window.location.hostname) {
                e.preventDefault();
                this.transitionToPage(link.href);
            }
        });
    },

    transitionToPage(url) {
        const transition = document.querySelector('.page-transition');
        transition.classList.add('active');

        setTimeout(() => {
            window.location.href = url;
        }, 300);
    }
};

// Custom Cursor System
StudyNotes.cursor = {
    init() {
        this.createCursor();
        this.setupCursorEvents();
        this.setupCursorTrail();
    },

    createCursor() {
        // Create main cursor
        this.cursor = document.createElement('div');
        this.cursor.className = 'custom-cursor';
        document.body.appendChild(this.cursor);

        // Track mouse position
        document.addEventListener('mousemove', (e) => {
            this.cursor.style.left = e.clientX + 'px';
            this.cursor.style.top = e.clientY + 'px';
        });
    },

    setupCursorEvents() {
        // Hover effects for interactive elements
        const interactiveElements = 'a, button, .btn, input, textarea, select, .card-interactive, .magnetic';

        document.addEventListener('mouseover', (e) => {
            if (e.target.matches(interactiveElements)) {
                this.cursor.classList.add('hover');
            }

            if (e.target.matches('input[type="text"], textarea')) {
                this.cursor.classList.add('text');
            }

            if (e.target.matches('[draggable="true"], .sortable-item')) {
                this.cursor.classList.add('drag');
            }

            if (e.target.matches(':disabled, .disabled')) {
                this.cursor.classList.add('disabled');
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches(interactiveElements)) {
                this.cursor.classList.remove('hover', 'text', 'drag', 'disabled');
            }
        });

        // Click effects
        document.addEventListener('mousedown', () => {
            this.cursor.classList.add('click');
        });

        document.addEventListener('mouseup', () => {
            this.cursor.classList.remove('click');
        });
    },

    setupCursorTrail() {
        this.trailElements = [];
        const trailLength = 10;

        // Create trail elements
        for (let i = 0; i < trailLength; i++) {
            const trail = document.createElement('div');
            trail.className = 'cursor-trail';
            trail.style.opacity = (trailLength - i) / trailLength * 0.5;
            document.body.appendChild(trail);
            this.trailElements.push(trail);
        }

        let mouseX = 0, mouseY = 0;
        let trailX = [], trailY = [];

        // Initialize trail positions
        for (let i = 0; i < trailLength; i++) {
            trailX[i] = 0;
            trailY[i] = 0;
        }

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        // Animate trail
        const animateTrail = () => {
            trailX[0] = mouseX;
            trailY[0] = mouseY;

            for (let i = 1; i < trailLength; i++) {
                trailX[i] += (trailX[i - 1] - trailX[i]) * 0.3;
                trailY[i] += (trailY[i - 1] - trailY[i]) * 0.3;

                this.trailElements[i].style.left = trailX[i] + 'px';
                this.trailElements[i].style.top = trailY[i] + 'px';
            }

            requestAnimationFrame(animateTrail);
        };

        animateTrail();
    }
};

// Theme System
StudyNotes.theme = {
    init() {
        this.currentTheme = localStorage.getItem('studynotes-theme') || 'light';
        this.applyTheme(this.currentTheme);
        this.createThemeToggle();
    },

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('studynotes-theme', theme);
    },

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);

        // Animate theme transition
        document.body.style.transition = 'none';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 100);
    },

    createThemeToggle() {
        const toggle = document.createElement('button');
        toggle.className = 'theme-toggle';
        toggle.innerHTML = this.currentTheme === 'light' ? '🌙' : '☀️';
        toggle.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--card-color);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        `;

        toggle.addEventListener('click', () => {
            this.toggleTheme();
            toggle.innerHTML = this.currentTheme === 'light' ? '🌙' : '☀️';
        });

        document.body.appendChild(toggle);
    }
};

// Advanced Animation System
StudyNotes.animations = {
    init() {
        this.setupRippleEffects();
        this.setupCounterAnimations();
        this.setupProgressAnimations();
        this.setupMorphingShapes();
    },

    // Ripple effect for buttons
    setupRippleEffects() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn, .ripple-effect')) {
                this.createRipple(e);
            }
        });
    },

    createRipple(e) {
        const button = e.target;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    },

    // Animated counters
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.counter');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    },

    animateCounter(element) {
        const target = parseInt(element.dataset.target) || 0;
        const duration = parseInt(element.dataset.duration) || 2000;
        const start = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(easeOutQuart * target);

            element.textContent = current.toLocaleString();

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    },

    // Progress bar animations
    setupProgressAnimations() {
        const progressBars = document.querySelectorAll('.progress-bar');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateProgress(entry.target);
                }
            });
        });

        progressBars.forEach(bar => observer.observe(bar));
    },

    animateProgress(element) {
        const target = parseInt(element.dataset.progress) || 0;
        const duration = parseInt(element.dataset.duration) || 1500;
        const start = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const current = easeOutCubic * target;

            element.style.width = current + '%';

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    },

    // Morphing shape animations
    setupMorphingShapes() {
        const shapes = document.querySelectorAll('.morph-shape');

        shapes.forEach(shape => {
            shape.addEventListener('mouseenter', () => {
                shape.style.borderRadius = '50%';
                shape.style.transform = 'rotate(180deg) scale(1.1)';
            });

            shape.addEventListener('mouseleave', () => {
                shape.style.borderRadius = '10px';
                shape.style.transform = 'rotate(0deg) scale(1)';
            });
        });
    },

    // Particle system for special effects
    createParticles(element, count = 20) {
        const rect = element.getBoundingClientRect();

        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: var(--highlight-color);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                left: ${rect.left + rect.width / 2}px;
                top: ${rect.top + rect.height / 2}px;
            `;

            document.body.appendChild(particle);

            const angle = (Math.PI * 2 * i) / count;
            const velocity = 2 + Math.random() * 3;
            const life = 1000 + Math.random() * 1000;

            this.animateParticle(particle, angle, velocity, life);
        }
    },

    animateParticle(particle, angle, velocity, life) {
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = elapsed / life;

            if (progress >= 1) {
                particle.remove();
                return;
            }

            const distance = velocity * elapsed / 16;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;

            particle.style.transform = `translate(${x}px, ${y}px)`;
            particle.style.opacity = 1 - progress;

            requestAnimationFrame(animate);
        };

        requestAnimationFrame(animate);
    }
};

// Data Visualization Components
StudyNotes.charts = {
    // Create animated progress ring
    createProgressRing(element, percentage, options = {}) {
        const {
            size = 120,
            strokeWidth = 8,
            color = 'var(--highlight-color)',
            backgroundColor = 'var(--border-light)',
            duration = 1500
        } = options;

        const radius = (size - strokeWidth) / 2;
        const circumference = radius * 2 * Math.PI;

        element.innerHTML = `
            <svg width="${size}" height="${size}" class="progress-ring">
                <circle
                    stroke="${backgroundColor}"
                    stroke-width="${strokeWidth}"
                    fill="transparent"
                    r="${radius}"
                    cx="${size / 2}"
                    cy="${size / 2}"
                />
                <circle
                    stroke="${color}"
                    stroke-width="${strokeWidth}"
                    fill="transparent"
                    r="${radius}"
                    cx="${size / 2}"
                    cy="${size / 2}"
                    stroke-dasharray="${circumference} ${circumference}"
                    stroke-dashoffset="${circumference}"
                    stroke-linecap="round"
                    class="progress-ring-circle"
                    style="transition: stroke-dashoffset ${duration}ms ease-in-out"
                />
                <text
                    x="50%"
                    y="50%"
                    text-anchor="middle"
                    dy="0.3em"
                    font-size="18"
                    font-weight="bold"
                    fill="var(--text-color)"
                    class="progress-text"
                >${percentage}%</text>
            </svg>
        `;

        // Animate the ring
        setTimeout(() => {
            const circle = element.querySelector('.progress-ring-circle');
            const offset = circumference - (percentage / 100) * circumference;
            circle.style.strokeDashoffset = offset;
        }, 100);
    },

    // Create animated bar chart
    createBarChart(element, data, options = {}) {
        const {
            height = 200,
            barColor = 'var(--highlight-color)',
            labelColor = 'var(--text-color)',
            animationDelay = 100
        } = options;

        const maxValue = Math.max(...data.map(d => d.value));

        element.innerHTML = `
            <div class="bar-chart" style="height: ${height}px; display: flex; align-items: end; gap: 10px;">
                ${data.map((item, index) => `
                    <div class="bar-container" style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                        <div class="bar"
                             style="width: 100%; background: ${barColor}; transition: height 1s ease ${index * animationDelay}ms; height: 0;"
                             data-height="${(item.value / maxValue) * 100}%">
                        </div>
                        <label style="margin-top: 8px; font-size: 12px; color: ${labelColor};">${item.label}</label>
                    </div>
                `).join('')}
            </div>
        `;

        // Animate bars
        setTimeout(() => {
            element.querySelectorAll('.bar').forEach(bar => {
                bar.style.height = bar.dataset.height;
            });
        }, 100);
    }
};

// Drag and Drop System
StudyNotes.dragDrop = {
    init() {
        this.setupSortableContainers();
        this.setupDragAndDrop();
    },

    setupSortableContainers() {
        const containers = document.querySelectorAll('.sortable-container');

        containers.forEach(container => {
            this.makeSortable(container);
        });
    },

    makeSortable(container) {
        let draggedElement = null;

        // Make items draggable
        container.querySelectorAll('.sortable-item').forEach(item => {
            item.draggable = true;
        });

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('sortable-item')) {
                draggedElement = e.target;
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('sortable-item')) {
                e.target.classList.remove('dragging');
                draggedElement = null;
            }
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            if (draggedElement) {
                const afterElement = this.getDragAfterElement(container, e.clientY);
                if (afterElement == null) {
                    container.appendChild(draggedElement);
                } else {
                    container.insertBefore(draggedElement, afterElement);
                }
            }
        });

        container.addEventListener('dragenter', (e) => {
            e.preventDefault();
            container.classList.add('drag-over');
        });

        container.addEventListener('dragleave', (e) => {
            if (!container.contains(e.relatedTarget)) {
                container.classList.remove('drag-over');
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            container.classList.remove('drag-over');

            // Emit custom event
            StudyNotes.events.emit('sortable:change', {
                container: container,
                item: draggedElement,
                newIndex: Array.from(container.children).indexOf(draggedElement)
            });
        });
    },

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    },

    setupDragAndDrop() {
        // Global drag and drop for file uploads
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();

            if (e.dataTransfer.files.length > 0) {
                this.handleFileUpload(e.dataTransfer.files, e.target);
            }
        });
    },

    handleFileUpload(files, target) {
        const uploadZone = target.closest('.upload-zone');
        if (uploadZone) {
            StudyNotes.events.emit('files:dropped', {
                files: Array.from(files),
                target: uploadZone
            });
        }
    }
};

// Advanced Search System
StudyNotes.search = {
    init() {
        this.setupAdvancedSearch();
        this.setupLiveFiltering();
    },

    setupAdvancedSearch() {
        const searchInputs = document.querySelectorAll('.search-input');

        searchInputs.forEach(input => {
            const resultsContainer = input.parentElement.querySelector('.search-results');
            if (!resultsContainer) return;

            const debouncedSearch = StudyNotes.utils.debounce((query) => {
                this.performSearch(query, resultsContainer);
            }, 300);

            input.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    debouncedSearch(query);
                    resultsContainer.classList.add('show');
                } else {
                    resultsContainer.classList.remove('show');
                }
            });

            input.addEventListener('focus', () => {
                if (input.value.trim().length > 2) {
                    resultsContainer.classList.add('show');
                }
            });

            input.addEventListener('blur', () => {
                setTimeout(() => {
                    resultsContainer.classList.remove('show');
                }, 200);
            });
        });
    },

    async performSearch(query, resultsContainer) {
        try {
            // Simulate API call
            const results = await this.mockSearchAPI(query);
            this.displaySearchResults(results, resultsContainer);
        } catch (error) {
            console.error('Search error:', error);
        }
    },

    mockSearchAPI(query) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const mockData = [
                    'JavaScript Fundamentals',
                    'CSS Grid Layout',
                    'React Components',
                    'Node.js Backend',
                    'Python Data Science',
                    'Machine Learning Basics',
                    'Database Design',
                    'API Development'
                ];

                const filtered = mockData.filter(item =>
                    item.toLowerCase().includes(query.toLowerCase())
                );

                resolve(filtered);
            }, 100);
        });
    },

    displaySearchResults(results, container) {
        container.innerHTML = results.map(result => `
            <div class="search-result-item" onclick="StudyNotes.search.selectResult('${result}')">
                ${this.highlightQuery(result, container.previousElementSibling.value)}
            </div>
        `).join('');
    },

    highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    },

    selectResult(result) {
        StudyNotes.events.emit('search:select', { result });
        StudyNotes.utils.showNotification(`Selected: ${result}`, 'info');
    },

    setupLiveFiltering() {
        const filterInputs = document.querySelectorAll('[data-filter-target]');

        filterInputs.forEach(input => {
            const targetSelector = input.dataset.filterTarget;
            const target = document.querySelector(targetSelector);

            if (!target) return;

            const debouncedFilter = StudyNotes.utils.debounce((query) => {
                this.filterElements(target, query);
            }, 200);

            input.addEventListener('input', (e) => {
                debouncedFilter(e.target.value);
            });
        });
    },

    filterElements(container, query) {
        const items = container.querySelectorAll('.filterable-item');

        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());

            item.style.display = matches ? '' : 'none';

            if (matches && query) {
                item.innerHTML = this.highlightQuery(item.textContent, query);
            }
        });
    }
};

// Performance Optimization System
StudyNotes.performance = {
    init() {
        this.setupImageLazyLoading();
        this.setupProgressiveImageLoading();
        this.optimizeAnimations();
    },

    setupImageLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('lazy');
            imageObserver.observe(img);
        });
    },

    setupProgressiveImageLoading() {
        const images = document.querySelectorAll('.image-blur-load');

        images.forEach(img => {
            const fullImage = new Image();
            fullImage.onload = () => {
                img.src = fullImage.src;
                img.classList.add('loaded');
            };
            fullImage.src = img.dataset.fullSrc || img.src;
        });
    },

    optimizeAnimations() {
        // Reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.documentElement.style.setProperty('--transition-fast', '0.1s');
            document.documentElement.style.setProperty('--transition-normal', '0.2s');
        }

        // Pause animations when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                document.body.style.animationPlayState = 'paused';
            } else {
                document.body.style.animationPlayState = 'running';
            }
        });
    }
};

// Event System
StudyNotes.events = {
    init() {
        this.setupCustomEvents();
    },

    setupCustomEvents() {
        // Custom event for form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.tagName === 'FORM') {
                this.emit('form:submit', { form: e.target, event: e });
            }
        });

        // Custom event for modal operations
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                const modalId = e.target.dataset.modalOpen;
                StudyNotes.components.modal.open(modalId);
                this.emit('modal:open', { modalId, trigger: e.target });
            }
        });
    },

    emit(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    },

    on(eventName, callback) {
        document.addEventListener(eventName, callback);
    },

    off(eventName, callback) {
        document.removeEventListener(eventName, callback);
    }
};

// Legacy function compatibility
function setupModals(config) {
    console.warn('setupModals is deprecated. Use StudyNotes.components.modal instead.');
    // Provide basic compatibility
    if (config.addBtnId) {
        const btn = document.getElementById(config.addBtnId);
        if (btn) {
            btn.addEventListener('click', () => {
                StudyNotes.components.modal.open(config.addModalId);
            });
        }
    }
}

function openModal(modal) {
    console.warn('openModal is deprecated. Use StudyNotes.components.modal.open instead.');
    StudyNotes.components.modal.open(modal);
}

function closeModal(modal) {
    console.warn('closeModal is deprecated. Use StudyNotes.components.modal.close instead.');
    StudyNotes.components.modal.close(modal);
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => StudyNotes.init());
} else {
    StudyNotes.init();
}

// Additional legacy compatibility functions
function setupNotifications() {
    console.warn('setupNotifications is deprecated. Notifications are now handled automatically.');
}

function setupFormAutoSave(excludeModalId) {
    console.warn('setupFormAutoSave is deprecated. Auto-save is now handled automatically.');
}

function setupKeyboardShortcuts(shortcuts, tooltips) {
    console.warn('setupKeyboardShortcuts is deprecated. Use StudyNotes.events system instead.');
}

function setupDataTables() {
    console.warn('setupDataTables is deprecated. Use StudyNotes.components.table instead.');
}

// Export for global access
window.StudyNotes = StudyNotes;
