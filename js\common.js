/**
 * StudyNotes Enhanced JavaScript Architecture
 * Modern ES6+ implementation with improved error handling and performance
 */

// StudyNotes Application Namespace
const StudyNotes = {
    // Configuration
    config: {
        apiTimeout: 30000,
        debounceDelay: 300,
        animationDuration: 300,
        retryAttempts: 3
    },

    // State management
    state: {
        isLoading: false,
        activeModals: new Set(),
        formData: new Map(),
        eventListeners: new Map()
    },

    // Utility functions
    utils: {},

    // Component modules
    components: {},

    // API handling
    api: {},

    // Event system
    events: {},

    // Initialize application
    init() {
        this.utils.init();
        this.api.init();
        this.events.init();
        this.components.init();
        console.log('StudyNotes application initialized');
    }
};

// Enhanced Utility Functions
StudyNotes.utils = {
    init() {
        this.setupGlobalErrorHandling();
        this.setupPerformanceMonitoring();
    },

    // Debounce function for performance optimization
    debounce(func, wait = StudyNotes.config.debounceDelay) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function for scroll/resize events
    throttle(func, limit = 100) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Enhanced DOM element selection with error handling
    $(selector, context = document) {
        try {
            const elements = context.querySelectorAll(selector);
            return elements.length === 1 ? elements[0] : elements;
        } catch (error) {
            console.error(`Invalid selector: ${selector}`, error);
            return null;
        }
    },

    // Safe element creation
    createElement(tag, attributes = {}, content = '') {
        try {
            const element = document.createElement(tag);

            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'dataset') {
                    Object.entries(value).forEach(([dataKey, dataValue]) => {
                        element.dataset[dataKey] = dataValue;
                    });
                } else {
                    element.setAttribute(key, value);
                }
            });

            if (content) {
                element.innerHTML = content;
            }

            return element;
        } catch (error) {
            console.error('Error creating element:', error);
            return null;
        }
    },

    // Enhanced form validation
    validateForm(form) {
        if (!form) return { isValid: false, errors: ['Form not found'] };

        const errors = [];
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                errors.push(`${field.name || field.id || 'Field'} is required`);
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                errors.push('Please enter a valid email address');
                field.classList.add('is-invalid');
            }
        });

        return { isValid: errors.length === 0, errors };
    },

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Local storage with error handling
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Error saving to localStorage:', error);
                return false;
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Error reading from localStorage:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Error removing from localStorage:', error);
                return false;
            }
        }
    },

    // Global error handling
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showNotification('An unexpected error occurred', 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showNotification('An unexpected error occurred', 'error');
        });
    },

    // Performance monitoring
    setupPerformanceMonitoring() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
        }
    },

    // Enhanced notification system
    showNotification(message, type = 'info', duration = 5000) {
        const notification = this.createElement('div', {
            className: `alert alert-${type} notification-toast`,
            style: 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; opacity: 0; transform: translateX(100%); transition: all 0.3s ease;'
        }, message);

        document.body.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });

        // Auto remove
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);

        return notification;
    }
};

// Enhanced API System
StudyNotes.api = {
    init() {
        this.setupInterceptors();
    },

    // Setup request/response interceptors
    setupInterceptors() {
        // Add loading state management
        this.originalFetch = window.fetch;
        window.fetch = (...args) => {
            StudyNotes.state.isLoading = true;
            return this.originalFetch(...args)
                .finally(() => {
                    StudyNotes.state.isLoading = false;
                });
        };
    },

    // Enhanced fetch with retry logic and better error handling
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: StudyNotes.config.apiTimeout
        };

        const config = { ...defaultOptions, ...options };

        // Add timeout support
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);
        config.signal = controller.signal;

        let lastError;

        for (let attempt = 1; attempt <= StudyNotes.config.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, config);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                } else {
                    return await response.text();
                }

            } catch (error) {
                lastError = error;
                console.warn(`API request attempt ${attempt} failed:`, error.message);

                if (attempt < StudyNotes.config.retryAttempts && !error.name === 'AbortError') {
                    await this.delay(1000 * attempt); // Exponential backoff
                    continue;
                }

                break;
            }
        }

        clearTimeout(timeoutId);
        throw lastError;
    },

    // Utility delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // Convenience methods
    get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    },

    post(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    put(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
};

// Enhanced Component System
StudyNotes.components = {
    init() {
        this.modal.init();
        this.form.init();
        this.table.init();
        this.navigation.init();
    },

    // Enhanced Modal Component
    modal: {
        init() {
            this.setupEventListeners();
        },

        setupEventListeners() {
            // Close modal on backdrop click
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    this.close(e.target);
                }
            });

            // Close modal on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && StudyNotes.state.activeModals.size > 0) {
                    const modals = Array.from(StudyNotes.state.activeModals);
                    this.close(modals[modals.length - 1]);
                }
            });

            // Setup close buttons
            document.addEventListener('click', (e) => {
                if (e.target.matches('.modal-close, .btn-cancel')) {
                    const modal = e.target.closest('.modal');
                    if (modal) this.close(modal);
                }
            });
        },

        open(modalId) {
            const modal = StudyNotes.utils.$(modalId);
            if (!modal) {
                console.error(`Modal ${modalId} not found`);
                return;
            }

            modal.style.display = 'block';
            StudyNotes.state.activeModals.add(modal);
            document.body.style.overflow = 'hidden';

            // Focus first input
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Animate in
            requestAnimationFrame(() => {
                modal.style.opacity = '1';
            });
        },

        close(modal) {
            if (typeof modal === 'string') {
                modal = StudyNotes.utils.$(modal);
            }

            if (!modal) return;

            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
                StudyNotes.state.activeModals.delete(modal);

                if (StudyNotes.state.activeModals.size === 0) {
                    document.body.style.overflow = '';
                }
            }, StudyNotes.config.animationDuration);

            // Reset forms in modal
            const forms = modal.querySelectorAll('form');
            forms.forEach(form => form.reset());
        }
    },

    // Enhanced Form Component
    form: {
        init() {
            this.setupAutoSave();
            this.setupValidation();
        },

        setupAutoSave() {
            const debouncedSave = StudyNotes.utils.debounce((form) => {
                const formId = form.id || `form_${Date.now()}`;
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                StudyNotes.utils.storage.set(`autosave_${formId}`, data);
            });

            document.addEventListener('input', (e) => {
                const form = e.target.closest('form');
                if (form && !form.classList.contains('no-autosave')) {
                    debouncedSave(form);
                }
            });
        },

        setupValidation() {
            document.addEventListener('submit', (e) => {
                const form = e.target;
                if (form.tagName === 'FORM') {
                    const validation = StudyNotes.utils.validateForm(form);
                    if (!validation.isValid) {
                        e.preventDefault();
                        StudyNotes.utils.showNotification(
                            validation.errors.join('<br>'),
                            'danger'
                        );
                    }
                }
            });
        },

        restoreAutoSave(formId) {
            const form = StudyNotes.utils.$(`#${formId}`);
            if (!form) return;

            const savedData = StudyNotes.utils.storage.get(`autosave_${formId}`);
            if (savedData) {
                Object.entries(savedData).forEach(([name, value]) => {
                    const field = form.querySelector(`[name="${name}"]`);
                    if (field) field.value = value;
                });
            }
        }
    },

    // Enhanced Table Component
    table: {
        init() {
            this.setupSorting();
            this.setupFiltering();
        },

        setupSorting() {
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-sort]')) {
                    const column = e.target.dataset.sort;
                    const table = e.target.closest('table');
                    this.sortTable(table, column);
                }
            });
        },

        setupFiltering() {
            const debouncedFilter = StudyNotes.utils.debounce((input) => {
                const table = document.querySelector(input.dataset.filterTable);
                if (table) {
                    this.filterTable(table, input.value);
                }
            });

            document.addEventListener('input', (e) => {
                if (e.target.matches('[data-filter-table]')) {
                    debouncedFilter(e.target);
                }
            });
        },

        sortTable(table, column) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            const isAscending = !table.dataset.sortDirection || table.dataset.sortDirection === 'desc';
            table.dataset.sortDirection = isAscending ? 'asc' : 'desc';

            rows.sort((a, b) => {
                const aVal = a.querySelector(`[data-${column}]`)?.textContent || '';
                const bVal = b.querySelector(`[data-${column}]`)?.textContent || '';

                const comparison = aVal.localeCompare(bVal, undefined, { numeric: true });
                return isAscending ? comparison : -comparison;
            });

            rows.forEach(row => tbody.appendChild(row));
        },

        filterTable(table, searchTerm) {
            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const matches = text.includes(searchTerm.toLowerCase());
                row.style.display = matches ? '' : 'none';
            });
        }
    },

    // Enhanced Navigation Component
    navigation: {
        init() {
            this.setupActiveStates();
            this.setupMobileMenu();
        },

        setupActiveStates() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar-menu a, .nav-menu a');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    link.classList.add('active');
                }
            });
        },

        setupMobileMenu() {
            const toggleBtn = document.querySelector('.mobile-menu-toggle');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn && sidebar) {
                toggleBtn.addEventListener('click', () => {
                    sidebar.classList.toggle('mobile-open');
                });

                // Close on outside click
                document.addEventListener('click', (e) => {
                    if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                        sidebar.classList.remove('mobile-open');
                    }
                });
            }
        }
    }
};

// Event System
StudyNotes.events = {
    init() {
        this.setupCustomEvents();
    },

    setupCustomEvents() {
        // Custom event for form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.tagName === 'FORM') {
                this.emit('form:submit', { form: e.target, event: e });
            }
        });

        // Custom event for modal operations
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                const modalId = e.target.dataset.modalOpen;
                StudyNotes.components.modal.open(modalId);
                this.emit('modal:open', { modalId, trigger: e.target });
            }
        });
    },

    emit(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    },

    on(eventName, callback) {
        document.addEventListener(eventName, callback);
    },

    off(eventName, callback) {
        document.removeEventListener(eventName, callback);
    }
};

// Legacy function compatibility
function setupModals(config) {
    console.warn('setupModals is deprecated. Use StudyNotes.components.modal instead.');
    // Provide basic compatibility
    if (config.addBtnId) {
        const btn = document.getElementById(config.addBtnId);
        if (btn) {
            btn.addEventListener('click', () => {
                StudyNotes.components.modal.open(config.addModalId);
            });
        }
    }
}

function openModal(modal) {
    console.warn('openModal is deprecated. Use StudyNotes.components.modal.open instead.');
    StudyNotes.components.modal.open(modal);
}

function closeModal(modal) {
    console.warn('closeModal is deprecated. Use StudyNotes.components.modal.close instead.');
    StudyNotes.components.modal.close(modal);
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => StudyNotes.init());
} else {
    StudyNotes.init();
}

// Additional legacy compatibility functions
function setupNotifications() {
    console.warn('setupNotifications is deprecated. Notifications are now handled automatically.');
}

function setupFormAutoSave(excludeModalId) {
    console.warn('setupFormAutoSave is deprecated. Auto-save is now handled automatically.');
}

function setupKeyboardShortcuts(shortcuts, tooltips) {
    console.warn('setupKeyboardShortcuts is deprecated. Use StudyNotes.events system instead.');
}

function setupDataTables() {
    console.warn('setupDataTables is deprecated. Use StudyNotes.components.table instead.');
}

// Export for global access
window.StudyNotes = StudyNotes;
