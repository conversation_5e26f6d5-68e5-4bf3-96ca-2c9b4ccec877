# StudyNotes Advanced Features Troubleshooting Guide

## Quick Diagnosis

### 1. Check if Features are Working
Open your browser's developer console (F12) and run:
```javascript
debugStudyNotes()
```

This will show you exactly what's working and what isn't.

### 2. Enable Advanced Features
If features aren't working, try enabling them manually:
```javascript
enableAdvancedFeatures()
```

### 3. Check for Errors
Look in the browser console for any JavaScript errors (red text).

## Common Issues & Solutions

### Issue 1: Custom Cursor Not Appearing
**Symptoms:** Default cursor still visible, no custom cursor effects

**Solutions:**
1. **Enable custom cursor manually:**
   ```javascript
   document.body.classList.add('custom-cursor-enabled');
   ```

2. **Check if CSS is loaded:**
   - Open Developer Tools → Elements
   - Look for `<link rel="stylesheet" href="css/common.css">`
   - If missing, add it to your HTML head section

3. **Disable conflicting cursor styles:**
   ```css
   * { cursor: auto !important; }
   ```

### Issue 2: Animations Not Working
**Symptoms:** Elements appear without animation, no smooth transitions

**Solutions:**
1. **Check CSS loading order:**
   ```html
   <link rel="stylesheet" href="css/style.css">
   <link rel="stylesheet" href="css/common.css">
   ```

2. **Add animation classes manually:**
   ```html
   <div class="animate-on-scroll">Your content</div>
   ```

3. **Force animation initialization:**
   ```javascript
   StudyNotes.ui && StudyNotes.ui.setupScrollAnimations();
   ```

### Issue 3: Theme Toggle Not Appearing
**Symptoms:** No theme toggle button visible

**Solutions:**
1. **Enable advanced features first:**
   ```javascript
   StudyNotes.enableAdvancedFeatures();
   ```

2. **Create theme toggle manually:**
   ```javascript
   StudyNotes.theme && StudyNotes.theme.createThemeToggle();
   ```

3. **Check for positioning conflicts:**
   ```css
   .theme-toggle {
       position: fixed !important;
       top: 20px !important;
       right: 20px !important;
       z-index: 9999 !important;
   }
   ```

### Issue 4: Button Effects Not Working
**Symptoms:** Buttons don't have ripple effects or hover animations

**Solutions:**
1. **Add enhanced classes:**
   ```html
   <!-- Instead of -->
   <button class="btn">Click me</button>
   
   <!-- Use -->
   <button class="btn enhanced ripple-effect">Click me</button>
   ```

2. **Check for CSS conflicts:**
   - Look for `!important` declarations overriding styles
   - Ensure `css/common.css` loads after `css/style.css`

### Issue 5: Drag & Drop Not Working
**Symptoms:** Items can't be dragged or dropped

**Solutions:**
1. **Add required classes:**
   ```html
   <div class="sortable-container">
       <div class="sortable-item" draggable="true">Item 1</div>
       <div class="sortable-item" draggable="true">Item 2</div>
   </div>
   ```

2. **Initialize drag & drop:**
   ```javascript
   StudyNotes.dragDrop && StudyNotes.dragDrop.init();
   ```

### Issue 6: Search Not Working
**Symptoms:** Search dropdown doesn't appear, no live filtering

**Solutions:**
1. **Use correct HTML structure:**
   ```html
   <div class="search-container">
       <input type="text" class="search-input" placeholder="Search...">
       <i class="fas fa-search search-icon"></i>
       <div class="search-results"></div>
   </div>
   ```

2. **Initialize search system:**
   ```javascript
   StudyNotes.search && StudyNotes.search.init();
   ```

## Integration Steps

### Step 1: Update HTML Files
Add the compatibility layer to your existing pages:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page</title>
    <!-- Load CSS in correct order -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/dashboard.css"> <!-- if needed -->
</head>
<body>
    <!-- Your existing content -->
    
    <!-- Load JavaScript -->
    <script src="js/common.js"></script>
    <script src="js/compatibility-layer.js"></script>
    <script src="js/shared-modal.js"></script> <!-- if needed -->
    <script src="js/user.js"></script> <!-- if needed -->
    
    <!-- Enable advanced features -->
    <script>
        // Optional: Enable advanced features
        document.addEventListener('DOMContentLoaded', function() {
            // Add ?advanced=true to URL to enable automatically
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('advanced') === 'true') {
                enableAdvancedFeatures();
            }
        });
    </script>
</body>
</html>
```

### Step 2: Enable Features Gradually

#### Option A: Enable for Specific Pages
Add to individual pages where you want advanced features:
```javascript
<script>
document.addEventListener('DOMContentLoaded', function() {
    enableAdvancedFeatures();
});
</script>
```

#### Option B: Enable via URL Parameter
Visit any page with `?advanced=true` to enable features:
```
http://localhost/studynotes/dashboard.php?advanced=true
```

#### Option C: Enable Globally
Add to all pages in a common header/footer:
```javascript
StudyNotes.config.enableAdvancedFeatures = true;
```

### Step 3: Add Enhanced Classes

#### Enhance Existing Buttons
```html
<!-- Before -->
<button class="btn">Submit</button>

<!-- After -->
<button class="btn enhanced ripple-effect magnetic">Submit</button>
```

#### Add Animations to Sections
```html
<!-- Before -->
<div class="stats-grid">

<!-- After -->
<div class="stats-grid animate-on-scroll">
```

#### Enhance Cards
```html
<!-- Before -->
<div class="card">

<!-- After -->
<div class="card card-interactive tilt-effect">
```

## Debugging Commands

### Browser Console Commands
```javascript
// Check what's working
debugStudyNotes()

// Enable advanced features
enableAdvancedFeatures()

// Disable advanced features
disableAdvancedFeatures()

// Check if StudyNotes is loaded
console.log(typeof StudyNotes)

// Check CSS variables
console.log(getComputedStyle(document.documentElement).getPropertyValue('--primary-color'))

// Force initialization
StudyNotes.init()

// Check for errors
StudyNotes.utils.setupGlobalErrorHandling()
```

### URL Parameters for Testing
```
?debug=true          - Show debug information in console
?advanced=true       - Enable advanced features automatically
?theme=dark          - Start with dark theme
?cursor=false        - Disable custom cursor
```

## Performance Optimization

### If Animations are Slow
```javascript
// Reduce animation duration
document.documentElement.style.setProperty('--transition-fast', '0.1s');
document.documentElement.style.setProperty('--transition-normal', '0.2s');

// Disable heavy effects on mobile
if (window.innerWidth < 768) {
    disableAdvancedFeatures();
}
```

### If Page Load is Slow
```html
<!-- Load CSS asynchronously -->
<link rel="preload" href="css/common.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

<!-- Load JavaScript after page load -->
<script>
window.addEventListener('load', function() {
    const script = document.createElement('script');
    script.src = 'js/common.js';
    document.head.appendChild(script);
});
</script>
```

## Browser Compatibility

### Modern Browsers (Recommended)
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

### Older Browsers
Advanced features will be automatically disabled. Basic functionality remains intact.

### Mobile Browsers
All features work on mobile with touch-optimized interactions.

## Getting Help

### Check Browser Console
1. Press F12 to open Developer Tools
2. Go to Console tab
3. Look for red error messages
4. Run `debugStudyNotes()` for detailed information

### Common Error Messages

**"StudyNotes is not defined"**
- Solution: Ensure `js/common.js` is loaded before other scripts

**"Cannot read property 'init' of undefined"**
- Solution: Enable advanced features first with `enableAdvancedFeatures()`

**"CSS variables not supported"**
- Solution: Use a modern browser or add CSS variable polyfill

### Test Pages
- `advanced_demo.html` - Full feature demonstration
- `test_improvements.html` - Basic feature testing

### Support Files
- `js/compatibility-layer.js` - Debugging and compatibility utilities
- `ADVANCED_FEATURES_GUIDE.md` - Complete feature documentation
- `ADVANCED_INTEGRATION_CHECKLIST.md` - Step-by-step integration guide

The advanced features are designed to enhance the existing StudyNotes application without breaking current functionality. If you encounter issues, start with the debugging commands and gradually enable features as needed.
