<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes Frontend Improvements Test</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .demo-section {
            margin-bottom: var(--space-2xl);
            padding: var(--space-lg);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-md);
            margin-top: var(--space-md);
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="text-center p-4 bg-primary text-white">
            <h1>StudyNotes Frontend Improvements</h1>
            <p>Demonstration of enhanced CSS and JavaScript features</p>
        </header>

        <main class="p-4">
            <!-- Button System Demo -->
            <section class="demo-section">
                <h2>Enhanced Button System</h2>
                <div class="demo-grid">
                    <div>
                        <h4>Button Variants</h4>
                        <div class="d-flex flex-column gap-2">
                            <button class="btn btn-primary">Primary Button</button>
                            <button class="btn btn-secondary">Secondary Button</button>
                            <button class="btn btn-outline">Outline Button</button>
                            <button class="btn btn-danger">Danger Button</button>
                            <button class="btn btn-success">Success Button</button>
                        </div>
                    </div>
                    <div>
                        <h4>Button Sizes</h4>
                        <div class="d-flex flex-column gap-2">
                            <button class="btn btn-primary btn-sm">Small Button</button>
                            <button class="btn btn-primary">Default Button</button>
                            <button class="btn btn-primary btn-lg">Large Button</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Form System Demo -->
            <section class="demo-section">
                <h2>Enhanced Form System</h2>
                <form class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label required">Name</label>
                            <input type="text" class="form-control" name="name" required>
                            <div class="form-text">Enter your full name</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Subject</label>
                            <select class="form-control form-select" name="subject">
                                <option value="">Choose a subject</option>
                                <option value="math">Mathematics</option>
                                <option value="science">Science</option>
                                <option value="history">History</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" name="message" rows="4"></textarea>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to newsletter
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Submit Form
                            </button>
                        </div>
                    </div>
                </form>
            </section>

            <!-- Card System Demo -->
            <section class="demo-section">
                <h2>Enhanced Card System</h2>
                <div class="demo-grid">
                    <div class="card">
                        <div class="card-header">
                            <h4>Study Note</h4>
                        </div>
                        <div class="card-body">
                            <p>This is an example of the enhanced card component with improved styling and hover effects.</p>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Created 2 hours ago</small>
                            <div>
                                <button class="btn btn-sm btn-primary">Edit</button>
                                <button class="btn btn-sm btn-outline">View</button>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h4>Quiz Results</h4>
                        </div>
                        <div class="card-body">
                            <p>Enhanced card with better spacing, typography, and interactive elements.</p>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">Score: 85%</small>
                            <div>
                                <button class="btn btn-sm btn-success">Retake</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Modal Demo -->
            <section class="demo-section">
                <h2>Enhanced Modal System</h2>
                <button class="btn btn-primary" data-modal-open="#demoModal">
                    <i class="fas fa-plus"></i>
                    Open Modal
                </button>
                <button class="btn btn-secondary" onclick="StudyNotes.utils.showNotification('This is a test notification!', 'success')">
                    <i class="fas fa-bell"></i>
                    Show Notification
                </button>
            </section>

            <!-- Utility Classes Demo -->
            <section class="demo-section">
                <h2>Utility Classes</h2>
                <div class="row">
                    <div class="col-md-4">
                        <h4>Spacing</h4>
                        <div class="p-3 bg-info text-white m-2">Padding 3</div>
                        <div class="p-2 bg-warning text-dark m-1">Padding 2</div>
                    </div>
                    <div class="col-md-4">
                        <h4>Text Utilities</h4>
                        <p class="text-primary">Primary text</p>
                        <p class="text-success">Success text</p>
                        <p class="text-danger">Danger text</p>
                        <p class="text-muted">Muted text</p>
                    </div>
                    <div class="col-md-4">
                        <h4>Flexbox</h4>
                        <div class="d-flex justify-content-between align-items-center p-2 bg-secondary text-white">
                            <span>Left</span>
                            <span>Center</span>
                            <span>Right</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Demo Modal -->
    <div id="demoModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Enhanced Modal</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>This modal demonstrates the enhanced modal system with:</p>
                    <ul>
                        <li>Backdrop blur effect</li>
                        <li>Smooth animations</li>
                        <li>Keyboard navigation (ESC to close)</li>
                        <li>Focus management</li>
                        <li>Responsive design</li>
                    </ul>
                    <div class="form-group">
                        <label class="form-label">Test Input</label>
                        <input type="text" class="form-control" placeholder="Focus is automatically set here">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Cancel</button>
                    <button class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // Demo specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Frontend improvements test page loaded');
            
            // Test form validation
            document.querySelector('form').addEventListener('submit', function(e) {
                e.preventDefault();
                StudyNotes.utils.showNotification('Form validation passed! (Demo only)', 'success');
            });
            
            // Test API functionality
            document.querySelector('[data-modal-open]').addEventListener('click', function() {
                console.log('Modal opened using new system');
            });
        });
    </script>
</body>
</html>
