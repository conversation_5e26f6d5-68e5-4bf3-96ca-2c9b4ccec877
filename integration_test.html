<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes Integration Test</title>
    <!-- Load CSS in correct order -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid var(--border-color, #ddd);
            border-radius: 8px;
            background: var(--card-color, white);
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-pass { background: #d4edda; color: #155724; }
        .test-fail { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
        .status-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>StudyNotes Integration Test</h1>
                <p class="tagline">Testing Advanced Features Compatibility</p>
            </div>
        </header>

        <main>
            <div class="status-indicator" id="statusIndicator">
                <div>Status: <span id="statusText">Loading...</span></div>
                <div>Advanced Features: <span id="advancedStatus">Checking...</span></div>
            </div>

            <!-- Basic Compatibility Test -->
            <div class="test-section">
                <h2>1. Basic Compatibility Test</h2>
                <p>Testing if existing StudyNotes functionality works with enhanced CSS/JS:</p>
                
                <div class="test-result" id="basicTest">Running tests...</div>
                
                <!-- Test existing button styles -->
                <div style="margin: 1rem 0;">
                    <button class="btn">Original Button</button>
                    <button class="btn enhanced">Enhanced Button</button>
                    <button class="btn enhanced ripple-effect">Ripple Button</button>
                </div>
            </div>

            <!-- CSS Variables Test -->
            <div class="test-section">
                <h2>2. CSS Variables Test</h2>
                <p>Checking if CSS variables are properly loaded:</p>
                
                <div class="test-result" id="cssTest">Running tests...</div>
                
                <div style="margin: 1rem 0;">
                    <div style="background: var(--primary-color); color: var(--light-text); padding: 10px; border-radius: 4px;">
                        Primary Color Background
                    </div>
                </div>
            </div>

            <!-- Animation Test -->
            <div class="test-section">
                <h2>3. Animation Test</h2>
                <p>Testing scroll animations and transitions:</p>
                
                <div class="test-result" id="animationTest">Running tests...</div>
                
                <div class="animate-on-scroll" style="margin: 1rem 0; padding: 1rem; background: var(--background-alt, #f8f9fa); border-radius: 4px;">
                    This element should animate when scrolled into view
                </div>
            </div>

            <!-- Advanced Features Test -->
            <div class="test-section">
                <h2>4. Advanced Features Test</h2>
                <p>Testing advanced features when enabled:</p>
                
                <div class="test-result" id="advancedTest">Running tests...</div>
                
                <div style="margin: 1rem 0;">
                    <button class="btn" onclick="testEnableAdvanced()">Enable Advanced Features</button>
                    <button class="btn" onclick="testDisableAdvanced()">Disable Advanced Features</button>
                </div>
                
                <div id="advancedFeaturesList" style="margin: 1rem 0;"></div>
            </div>

            <!-- Interactive Elements Test -->
            <div class="test-section">
                <h2>5. Interactive Elements Test</h2>
                <p>Testing enhanced interactive elements:</p>
                
                <div class="test-result" id="interactiveTest">Running tests...</div>
                
                <!-- Test cards -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                    <div class="card">
                        <div class="card-body" style="padding: 1rem;">
                            <h4>Basic Card</h4>
                            <p>Standard card styling</p>
                        </div>
                    </div>
                    <div class="card card-interactive">
                        <div class="card-body" style="padding: 1rem;">
                            <h4>Interactive Card</h4>
                            <p>Enhanced with hover effects</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debug Information -->
            <div class="test-section">
                <h2>6. Debug Information</h2>
                <p>Detailed system information:</p>
                
                <div class="test-result" id="debugInfo">Loading debug info...</div>
                
                <button class="btn" onclick="runFullDiagnostic()">Run Full Diagnostic</button>
                <button class="btn" onclick="showConsoleCommands()">Show Console Commands</button>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 StudyNotes Integration Test</p>
        </footer>
    </div>

    <!-- Load JavaScript -->
    <script src="js/common.js"></script>
    <script src="js/compatibility-layer.js"></script>
    
    <script>
        // Test functions
        let testResults = {};

        function updateStatus(text, isAdvanced = false) {
            document.getElementById('statusText').textContent = text;
            if (isAdvanced !== null) {
                document.getElementById('advancedStatus').textContent = isAdvanced ? 'Enabled' : 'Disabled';
            }
        }

        function runBasicTest() {
            const results = [];
            
            // Test 1: Check if StudyNotes object exists
            if (typeof StudyNotes !== 'undefined') {
                results.push('✅ StudyNotes object loaded');
            } else {
                results.push('❌ StudyNotes object not found');
            }
            
            // Test 2: Check if CSS is loaded
            const testEl = document.createElement('div');
            testEl.className = 'btn';
            document.body.appendChild(testEl);
            const styles = getComputedStyle(testEl);
            if (styles.display === 'inline-flex') {
                results.push('✅ CSS styles loaded correctly');
            } else {
                results.push('❌ CSS styles not loaded properly');
            }
            document.body.removeChild(testEl);
            
            // Test 3: Check initialization
            if (StudyNotes && StudyNotes.state && StudyNotes.state.initialized) {
                results.push('✅ StudyNotes initialized');
            } else {
                results.push('⚠️ StudyNotes not initialized (this is normal)');
            }
            
            document.getElementById('basicTest').innerHTML = results.join('<br>');
            testResults.basic = results;
        }

        function runCSSTest() {
            const results = [];
            const root = getComputedStyle(document.documentElement);
            
            const variables = [
                '--primary-color',
                '--secondary-color', 
                '--highlight-color',
                '--text-color',
                '--background-color'
            ];
            
            variables.forEach(variable => {
                const value = root.getPropertyValue(variable);
                if (value) {
                    results.push(`✅ ${variable}: ${value.trim()}`);
                } else {
                    results.push(`❌ ${variable}: not found`);
                }
            });
            
            document.getElementById('cssTest').innerHTML = results.join('<br>');
            testResults.css = results;
        }

        function runAnimationTest() {
            const results = [];
            
            // Test animation classes
            const animEl = document.querySelector('.animate-on-scroll');
            if (animEl) {
                const styles = getComputedStyle(animEl);
                if (styles.opacity === '0') {
                    results.push('✅ Animation classes working');
                } else {
                    results.push('⚠️ Animation classes may not be working');
                }
            }
            
            // Test CSS animations
            const keyframes = document.styleSheets;
            let hasAnimations = false;
            try {
                for (let sheet of keyframes) {
                    for (let rule of sheet.cssRules || []) {
                        if (rule.type === CSSRule.KEYFRAMES_RULE) {
                            hasAnimations = true;
                            break;
                        }
                    }
                }
            } catch (e) {
                // Cross-origin restrictions
            }
            
            if (hasAnimations) {
                results.push('✅ CSS animations available');
            } else {
                results.push('⚠️ CSS animations not detected');
            }
            
            document.getElementById('animationTest').innerHTML = results.join('<br>');
            testResults.animation = results;
        }

        function testEnableAdvanced() {
            if (typeof enableAdvancedFeatures === 'function') {
                enableAdvancedFeatures();
                updateStatus('Advanced features enabled', true);
                runAdvancedTest();
            } else {
                alert('Advanced features function not available');
            }
        }

        function testDisableAdvanced() {
            if (typeof disableAdvancedFeatures === 'function') {
                disableAdvancedFeatures();
                updateStatus('Advanced features disabled', false);
                runAdvancedTest();
            } else {
                alert('Disable function not available');
            }
        }

        function runAdvancedTest() {
            const results = [];
            
            // Check if advanced features are enabled
            if (StudyNotes && StudyNotes.config && StudyNotes.config.enableAdvancedFeatures) {
                results.push('✅ Advanced features enabled');
                
                // Check individual features
                if (document.querySelector('.custom-cursor')) {
                    results.push('✅ Custom cursor active');
                } else {
                    results.push('⚠️ Custom cursor not found');
                }
                
                if (document.querySelector('.theme-toggle')) {
                    results.push('✅ Theme toggle created');
                } else {
                    results.push('⚠️ Theme toggle not found');
                }
                
                if (document.body.classList.contains('custom-cursor-enabled')) {
                    results.push('✅ Custom cursor enabled on body');
                } else {
                    results.push('⚠️ Custom cursor not enabled on body');
                }
                
            } else {
                results.push('❌ Advanced features disabled');
            }
            
            document.getElementById('advancedTest').innerHTML = results.join('<br>');
            testResults.advanced = results;
        }

        function runInteractiveTest() {
            const results = [];
            
            // Test card interactions
            const cards = document.querySelectorAll('.card');
            results.push(`✅ Found ${cards.length} cards`);
            
            const interactiveCards = document.querySelectorAll('.card-interactive');
            results.push(`✅ Found ${interactiveCards.length} interactive cards`);
            
            // Test button effects
            const enhancedButtons = document.querySelectorAll('.btn.enhanced');
            results.push(`✅ Found ${enhancedButtons.length} enhanced buttons`);
            
            document.getElementById('interactiveTest').innerHTML = results.join('<br>');
            testResults.interactive = results;
        }

        function runFullDiagnostic() {
            if (typeof debugStudyNotes === 'function') {
                const diagnostic = debugStudyNotes();
                const results = [
                    `CSS Loaded: ${diagnostic.cssLoaded ? '✅' : '❌'}`,
                    `JS Loaded: ${diagnostic.jsLoaded ? '✅' : '❌'}`,
                    `Advanced Features: ${diagnostic.advancedFeaturesEnabled ? '✅' : '❌'}`,
                    `Custom Cursor: ${diagnostic.customCursorWorking ? '✅' : '❌'}`,
                    `Animations: ${diagnostic.animationsWorking ? '✅' : '❌'}`,
                    `Theme System: ${diagnostic.themeSystemWorking ? '✅' : '❌'}`
                ];
                
                if (diagnostic.errors.length > 0) {
                    results.push(`Errors: ${diagnostic.errors.join(', ')}`);
                }
                
                document.getElementById('debugInfo').innerHTML = results.join('<br>');
            } else {
                document.getElementById('debugInfo').innerHTML = '❌ Debug function not available';
            }
        }

        function showConsoleCommands() {
            const commands = [
                'debugStudyNotes() - Show diagnostic info',
                'enableAdvancedFeatures() - Enable advanced features',
                'disableAdvancedFeatures() - Disable advanced features',
                'StudyNotes.init() - Initialize StudyNotes',
                'StudyNotes.config - View configuration'
            ];
            
            alert('Available Console Commands:\n\n' + commands.join('\n'));
        }

        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Running tests...');
            
            setTimeout(() => {
                runBasicTest();
                runCSSTest();
                runAnimationTest();
                runAdvancedTest();
                runInteractiveTest();
                
                updateStatus('Tests completed', StudyNotes && StudyNotes.config && StudyNotes.config.enableAdvancedFeatures);
            }, 500);
        });
    </script>
</body>
</html>
