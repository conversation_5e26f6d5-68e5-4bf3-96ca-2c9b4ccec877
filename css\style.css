/* Modern Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

:root {
    /* Warm Brown Color Palette - Original StudyNotes Theme */
    --primary-color: #65350F;        /* Dark Brown */
    --primary-dark: #4a2609;         /* Darker Brown */
    --primary-light: #8b4513;        /* Lighter Brown */
    --secondary-color: #A67B5B;      /* Medium Brown */
    --accent-color: #D4A373;         /* Light Brown */
    --highlight-color: #E8871E;      /* Orange */
    --success-color: #10b981;        /* Emerald */
    --warning-color: #f59e0b;        /* Amber */
    --danger-color: #ef4444;         /* Red */

    /* Text Colors */
    --text-color: #2D2424;           /* <PERSON> Text */
    --text-muted: #A67B5B;           /* <PERSON> for muted text */
    --text-light: #8B7355;           /* <PERSON> Brown for subtle text */
    --light-text: #F5EBE0;           /* Light Beige Text */

    /* Background Colors */
    --background-color: #F5EBE0;     /* Light Beige */
    --background-secondary: #F0E6D6; /* Slightly Darker Beige */
    --card-color: #FFFFFF;           /* White */
    --border-color: #E6D7C3;         /* Light Brown Border */

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    background: linear-gradient(135deg, var(--background-color) 0%, var(--background-secondary) 100%);
    color: var(--text-color);
    line-height: 1.6;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Add subtle warm background pattern */
.container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(101, 53, 15, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(212, 163, 115, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Modern Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--light-text);
    padding: var(--space-xl) var(--space-2xl);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

/* Add subtle header pattern */
header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.logo {
    position: relative;
    z-index: 1;
}

.logo h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-xs);
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    opacity: 0.9;
    margin-top: var(--space-xs);
}

/* Modern Main Content Styles */
main {
    flex: 1;
    padding: var(--space-2xl) var(--space-lg);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    position: relative;
    z-index: 1;
}

.welcome-section {
    text-align: center;
    margin-bottom: var(--space-2xl);
    padding: var(--space-2xl) 0;
}

.welcome-section h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--space-lg);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--highlight-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-section p {
    max-width: 600px;
    margin: 0 auto var(--space-xl);
    font-size: var(--font-size-lg);
    color: var(--text-muted);
    line-height: 1.7;
}

/* Modern Features Section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
    padding: 0 var(--space-md);
}

.feature-card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--highlight-color) 100%);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.feature-card i {
    font-size: var(--font-size-4xl);
    background: linear-gradient(135deg, var(--highlight-color) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-lg);
    display: block;
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-md);
    color: var(--text-color);
}

.feature-card p {
    color: var(--text-muted);
    font-size: var(--font-size-base);
    line-height: 1.6;
}

/* Modern Access Section */
.access-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
    padding: 0 var(--space-md);
    margin-bottom: var(--space-2xl);
}

.access-card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.access-card::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--highlight-color) 0%, var(--accent-color) 100%);
}

.access-card.admin::before {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.access-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.access-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-lg);
    color: var(--text-color);
}

.access-card p {
    margin-bottom: var(--space-xl);
    color: var(--text-muted);
    font-size: var(--font-size-base);
    line-height: 1.6;
}

/* Modern Button Styles with Warm Theme */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-md) var(--space-xl);
    background: linear-gradient(135deg, var(--highlight-color) 0%, var(--primary-color) 100%);
    color: var(--light-text);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    min-width: 140px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.btn i {
    margin-right: var(--space-sm);
    font-size: var(--font-size-sm);
}

/* Standardized slim buttons */
.btn-slim {
    padding: 4px 12px;
    font-size: 13px;
    height: 30px;
    line-height: 1.4;
    border-radius: 4px;
    font-weight: 500;
}

/* Action buttons (with + symbol) */
.btn-action {
    min-width: 160px;
    justify-content: center;
}

.btn-action i {
    font-size: 12px;
    margin-right: 6px;
}

/* Navigation buttons (back buttons) */
.btn-nav {
    min-width: 130px;
    justify-content: center;
}

.btn-nav i {
    font-size: 12px;
    margin-right: 6px;
}

.admin-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.admin-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
}

/* Modern Footer Styles */
footer {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--light-text);
    text-align: center;
    padding: var(--space-xl);
    margin-top: auto;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

footer p {
    position: relative;
    z-index: 1;
    margin: 0;
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* Modern Responsive Adjustments */
@media (max-width: 768px) {
    header {
        padding: var(--space-lg) var(--space-md);
    }

    .logo h1 {
        font-size: var(--font-size-3xl);
    }

    .tagline {
        font-size: var(--font-size-base);
    }

    main {
        padding: var(--space-lg) var(--space-md);
    }

    .welcome-section {
        padding: var(--space-lg) 0;
    }

    .welcome-section h2 {
        font-size: var(--font-size-2xl);
    }

    .features {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        padding: 0;
    }

    .feature-card {
        padding: var(--space-lg);
    }

    .feature-card i {
        font-size: var(--font-size-3xl);
    }

    .access-section {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
        padding: 0;
    }

    .access-card {
        padding: var(--space-lg);
    }

    .btn {
        padding: var(--space-sm) var(--space-lg);
        min-width: 120px;
    }
}