/**
 * StudyNotes Quick Fix Script
 * Add this script to any page experiencing issues with advanced features
 * Usage: <script src="js/quick-fix.js"></script>
 */

(function() {
    'use strict';
    
    console.log('StudyNotes Quick Fix Script loaded');
    
    // Fix 1: Ensure CSS variables are available
    function ensureCSSVariables() {
        const root = getComputedStyle(document.documentElement);
        const primaryColor = root.getPropertyValue('--primary-color');
        
        if (!primaryColor) {
            console.log('Adding missing CSS variables...');
            const style = document.createElement('style');
            style.id = 'studynotes-css-variables';
            style.textContent = `
                :root {
                    --primary-color: #65350F;
                    --secondary-color: #A67B5B;
                    --accent-color: #D4A373;
                    --highlight-color: #E8871E;
                    --text-color: #2D2424;
                    --light-text: #F5EBE0;
                    --background-color: #F5EBE0;
                    --card-color: #FFFFFF;
                    --danger-color: #dc3545;
                    --success-color: #28a745;
                    --warning-color: #ffc107;
                    --border-color: #E0E0E0;
                    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
                    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
                    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
                    --radius-sm: 0.25rem;
                    --radius-md: 0.5rem;
                    --radius-lg: 0.75rem;
                    --space-sm: 0.5rem;
                    --space-md: 1rem;
                    --space-lg: 1.5rem;
                    --space-xl: 2rem;
                    --transition-fast: 0.15s ease-in-out;
                    --transition-normal: 0.3s ease-in-out;
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // Fix 2: Resolve CSS conflicts
    function resolveCSSConflicts() {
        // Remove conflicting cursor styles
        const conflictingStyles = document.querySelectorAll('style[data-conflict]');
        conflictingStyles.forEach(style => style.remove());
        
        // Fix button conflicts
        const style = document.createElement('style');
        style.id = 'studynotes-css-fixes';
        style.textContent = `
            /* Fix button conflicts */
            .btn {
                position: relative;
                display: inline-block;
                padding: 8px 16px;
                margin: 2px;
                border: 1px solid transparent;
                border-radius: 4px;
                text-decoration: none;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            /* Fix card conflicts */
            .card {
                background: var(--card-color, white);
                border: 1px solid var(--border-color, #ddd);
                border-radius: var(--radius-md, 8px);
                box-shadow: var(--shadow-sm, 0 1px 3px rgba(0,0,0,0.1));
                transition: all 0.3s ease;
            }
            
            /* Fix animation conflicts */
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s ease;
            }
            
            .animate-on-scroll.animate-in {
                opacity: 1;
                transform: translateY(0);
            }
            
            /* Fix modal conflicts */
            .modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1000;
            }
            
            /* Ensure custom cursor doesn't interfere */
            body:not(.custom-cursor-enabled) {
                cursor: auto !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Fix 3: Initialize StudyNotes safely
    function initializeStudyNotes() {
        if (typeof StudyNotes !== 'undefined') {
            if (!StudyNotes.state || !StudyNotes.state.initialized) {
                try {
                    StudyNotes.init();
                    console.log('StudyNotes initialized successfully');
                } catch (error) {
                    console.warn('StudyNotes initialization error:', error);
                }
            }
        } else {
            console.warn('StudyNotes not found - advanced features unavailable');
        }
    }
    
    // Fix 4: Provide fallback functions
    function provideFallbacks() {
        // Modal fallbacks
        if (typeof openModal === 'undefined') {
            window.openModal = function(modal) {
                const modalElement = typeof modal === 'string' ? document.querySelector(modal) : modal;
                if (modalElement) {
                    modalElement.style.display = 'block';
                }
            };
        }
        
        if (typeof closeModal === 'undefined') {
            window.closeModal = function(modal) {
                const modalElement = typeof modal === 'string' ? document.querySelector(modal) : modal;
                if (modalElement) {
                    modalElement.style.display = 'none';
                }
            };
        }
        
        // Advanced features fallbacks
        if (typeof enableAdvancedFeatures === 'undefined') {
            window.enableAdvancedFeatures = function() {
                console.log('Advanced features not available - using fallback');
                if (typeof StudyNotes !== 'undefined' && StudyNotes.enableAdvancedFeatures) {
                    StudyNotes.enableAdvancedFeatures();
                }
            };
        }
        
        if (typeof debugStudyNotes === 'undefined') {
            window.debugStudyNotes = function() {
                console.log('Debug function not available');
                return {
                    cssLoaded: !!document.querySelector('link[href*="common.css"]'),
                    jsLoaded: typeof StudyNotes !== 'undefined',
                    advancedFeaturesEnabled: false,
                    errors: ['Debug function not loaded']
                };
            };
        }
    }
    
    // Fix 5: Handle scroll animations
    function setupScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, { threshold: 0.1 });
            
            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                observer.observe(el);
            });
        } else {
            // Fallback for older browsers
            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                el.classList.add('animate-in');
            });
        }
    }
    
    // Fix 6: Handle button enhancements
    function setupButtonEnhancements() {
        document.addEventListener('click', function(e) {
            if (e.target.matches('.btn.ripple-effect')) {
                const button = e.target;
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                const ripple = document.createElement('span');
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;
                
                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            }
        });
        
        // Add ripple animation if not exists
        if (!document.querySelector('#ripple-animation')) {
            const style = document.createElement('style');
            style.id = 'ripple-animation';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // Apply all fixes
    function applyAllFixes() {
        console.log('Applying StudyNotes fixes...');
        
        ensureCSSVariables();
        resolveCSSConflicts();
        provideFallbacks();
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                initializeStudyNotes();
                setupScrollAnimations();
                setupButtonEnhancements();
            });
        } else {
            initializeStudyNotes();
            setupScrollAnimations();
            setupButtonEnhancements();
        }
        
        console.log('StudyNotes fixes applied');
    }
    
    // Auto-apply fixes
    applyAllFixes();
    
    // Expose fix functions globally
    window.StudyNotesQuickFix = {
        ensureCSSVariables,
        resolveCSSConflicts,
        initializeStudyNotes,
        provideFallbacks,
        setupScrollAnimations,
        setupButtonEnhancements,
        applyAllFixes
    };
    
})();
