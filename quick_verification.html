<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes Quick Verification</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/common.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .test-item { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .test-controls { margin: 20px 0; }
        .test-controls button { margin: 5px; padding: 8px 16px; }
    </style>
</head>
<body>
    <h1>StudyNotes Quick Verification</h1>
    <p>This page performs rapid verification of all key features.</p>
    
    <div class="test-controls">
        <button onclick="runQuickTest()">Run Quick Test</button>
        <button onclick="enableAdvancedFeatures()">Enable Advanced Features</button>
        <button onclick="testAdvancedFeatures()">Test Advanced Features</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>
    
    <!-- Test elements -->
    <div style="display: none;">
        <button class="btn">Basic Button</button>
        <button class="btn enhanced">Enhanced Button</button>
        <div class="card">Basic Card</div>
        <div class="card enhanced">Enhanced Card</div>
        <div class="animate-on-scroll">Animation Element</div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/compatibility-layer.js"></script>
    
    <script>
        function addResult(message, type = 'pass') {
            const div = document.createElement('div');
            div.className = `test-item ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function runQuickTest() {
            clearResults();
            addResult('🚀 Starting Quick Verification Test');
            
            // Test 1: Basic CSS
            const btn = document.querySelector('.btn');
            const btnStyles = getComputedStyle(btn);
            if (btnStyles.display === 'inline-flex') {
                addResult('✅ Basic CSS loaded correctly');
            } else {
                addResult('❌ Basic CSS not loaded', 'fail');
            }
            
            // Test 2: StudyNotes object
            if (typeof StudyNotes !== 'undefined') {
                addResult('✅ StudyNotes object available');
                
                if (StudyNotes.utils && StudyNotes.components && StudyNotes.state) {
                    addResult('✅ StudyNotes structure complete');
                } else {
                    addResult('❌ StudyNotes structure incomplete', 'fail');
                }
            } else {
                addResult('❌ StudyNotes object not found', 'fail');
            }
            
            // Test 3: Compatibility layer
            if (typeof enableAdvancedFeatures === 'function') {
                addResult('✅ Compatibility layer loaded');
            } else {
                addResult('❌ Compatibility layer missing', 'fail');
            }
            
            // Test 4: CSS Variables
            const root = getComputedStyle(document.documentElement);
            const primaryColor = root.getPropertyValue('--primary-color');
            if (primaryColor) {
                addResult('✅ CSS variables working: ' + primaryColor.trim());
            } else {
                addResult('❌ CSS variables not found', 'fail');
            }
            
            // Test 5: Enhanced elements
            const enhancedBtn = document.querySelector('.btn.enhanced');
            const enhancedStyles = getComputedStyle(enhancedBtn);
            if (enhancedStyles.position === 'relative') {
                addResult('✅ Enhanced CSS styles loaded');
            } else {
                addResult('⚠️ Enhanced CSS styles not detected', 'warning');
            }
            
            // Test 6: Error handling
            try {
                StudyNotes.utils.$('invalid>>selector');
                addResult('✅ Error handling working (no crash on invalid selector)');
            } catch (error) {
                addResult('❌ Error handling failed: ' + error.message, 'fail');
            }
            
            addResult('🏁 Quick verification complete');
        }

        function testAdvancedFeatures() {
            clearResults();
            addResult('🔮 Testing Advanced Features');
            
            // Check if advanced features are enabled
            if (StudyNotes.config && StudyNotes.config.enableAdvancedFeatures) {
                addResult('✅ Advanced features enabled');
                
                // Test custom cursor
                const cursor = document.querySelector('.custom-cursor');
                if (cursor) {
                    addResult('✅ Custom cursor element found');
                } else {
                    addResult('⚠️ Custom cursor not found', 'warning');
                }
                
                // Test theme toggle
                const themeToggle = document.querySelector('.theme-toggle');
                if (themeToggle) {
                    addResult('✅ Theme toggle found');
                } else {
                    addResult('⚠️ Theme toggle not found', 'warning');
                }
                
                // Test body classes
                if (document.body.classList.contains('custom-cursor-enabled')) {
                    addResult('✅ Custom cursor enabled on body');
                } else {
                    addResult('⚠️ Custom cursor not enabled on body', 'warning');
                }
                
            } else {
                addResult('⚠️ Advanced features not enabled', 'warning');
                addResult('💡 Run enableAdvancedFeatures() first');
            }
            
            addResult('🏁 Advanced features test complete');
        }

        // Auto-run basic test on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runQuickTest, 500);
        });
    </script>
</body>
</html>
