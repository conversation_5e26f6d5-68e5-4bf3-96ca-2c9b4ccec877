/* Modern Dashboard Styles */

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: calc(100vh - 180px);
    gap: var(--space-xl);
}

.sidebar {
    width: 280px;
    background: var(--card-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    padding: var(--space-xl) 0;
    height: fit-content;
    position: sticky;
    top: var(--space-xl);
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    padding: 0 var(--space-md);
}

.sidebar-menu a {
    color: var(--text-color);
    text-decoration: none;
    padding: var(--space-md) var(--space-lg);
    display: flex;
    align-items: center;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal);
    position: relative;
}

.sidebar-menu a i {
    margin-right: var(--space-md);
    width: 20px;
    text-align: center;
    font-size: var(--font-size-base);
}

.sidebar-menu a:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--light-text);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.sidebar-menu a.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--light-text);
    box-shadow: var(--shadow-md);
}

.sidebar-menu a.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: var(--light-text);
    border-radius: 0 2px 2px 0;
}

.content {
    flex: 1;
    padding: 0;
    background: transparent;
}

/* Modern Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2xl);
    padding: var(--space-xl);
    background: var(--card-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.page-header h2 {
    color: var(--text-color);
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
}

.user-name {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    color: var(--light-text);
    text-decoration: none;
    background-color: var(--highlight-color);
    padding: 5px 10px;
    border-radius: 4px;
}

.logout-btn:hover {
    background-color: var(--primary-color);
}

/* Modern Stats Cards */
.stats-container,
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--space-lg);
    box-shadow: var(--shadow-md);
}

.stat-icon i {
    font-size: var(--font-size-2xl);
    color: var(--light-text);
}

.stat-info h3 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-color);
}

.stat-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

/* Modern Cards */
.card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.card-header {
    padding: var(--space-xl) var(--space-xl) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--background-secondary) 100%);
}

.card-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    border: 1px solid transparent;
}

.view-all:hover {
    background: var(--primary-color);
    color: var(--light-text);
    text-decoration: none;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.card-body {
    padding: var(--space-xl);
}

/* Dashboard-specific overrides - common styles moved to common.css */

/* Dashboard-specific note preview height override */
.note-preview {
    height: 60px;
    overflow: hidden;
}

/* Dashboard-specific module description height override */
.module-description {
    height: 60px;
    overflow: hidden;
}

/* No Data Message */
.no-data {
    text-align: center;
    padding: 30px;
    color: var(--secondary-color);
}

.no-data a {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 500;
}

.no-data a:hover {
    text-decoration: underline;
}

/* Message styles moved to common.css */

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: var(--card-color);
    margin: 10% auto;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus, .form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn:hover {
    background-color: var(--highlight-color);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    color: var(--text-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Slim buttons - Using standardized style from style.css */

.warning {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 10px;
    }

    .sidebar-menu a {
        padding: 10px 15px;
        white-space: nowrap;
    }

    .sidebar-menu a.active {
        border-left: none;
        border-bottom: 4px solid var(--light-text);
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
