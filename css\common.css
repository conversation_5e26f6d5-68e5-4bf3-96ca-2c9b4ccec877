/*
 * StudyNotes Design System - Enhanced CSS Architecture
 * Comprehensive design system with improved organization and modern CSS practices
 */

/* Enhanced CSS Variables - Complete Design System */
:root {
    /* Brand Colors - Use existing variables from style.css */
    /* --primary-color: #65350F; - Already defined in style.css */
    /* --secondary-color: #A67B5B; - Already defined in style.css */
    /* --accent-color: #D4A373; - Already defined in style.css */
    /* --highlight-color: #E8871E; - Already defined in style.css */
    /* --text-color: #2D2424; - Already defined in style.css */
    /* --light-text: #F5EBE0; - Already defined in style.css */
    /* --background-color: #F5EBE0; - Already defined in style.css */
    /* --card-color: #FFFFFF; - Already defined in style.css */

    /* Extended Brand Colors */
    --primary-light: #8B4513;       /* Lighter Brown */
    --primary-dark: #4A2C0A;        /* Darker <PERSON> */
    --highlight-light: #FFB347;     /* Light Orange */
    --highlight-dark: #CC7A00;      /* Dark Orange */

    /* Extended Text Colors */
    --text-light: #6B5B5B;          /* Light Text */
    --text-muted: #8E8E8E;          /* Muted Text */

    /* Extended Background Colors */
    --background-alt: #FAF0E6;      /* Alternative Background */
    --card-hover: #FAFAFA;          /* Card Hover */

    /* Status Colors */
    --success-color: #28a745;       /* Green */
    --success-light: #D4EDDA;       /* Light Green */
    --danger-color: #dc3545;        /* Red */
    --danger-light: #F8D7DA;        /* Light Red */
    --warning-color: #ffc107;       /* Yellow */
    --warning-light: #FFF3CD;       /* Light Yellow */
    --info-color: #17a2b8;          /* Blue */
    --info-light: #D1ECF1;          /* Light Blue */

    /* Border Colors */
    --border-color: #E0E0E0;        /* Default Border */
    --border-light: #F0F0F0;        /* Light Border */
    --border-dark: #CCCCCC;         /* Dark Border */

    /* Shadow Variables */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* Typography Scale */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-full: 9999px;  /* Full radius */

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-cursor: 9999;

    /* Advanced Animation Variables */
    --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --animation-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --animation-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --animation-sharp: cubic-bezier(0.4, 0, 0.6, 1);

    /* Glassmorphism Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-backdrop: blur(10px);

    /* Theme Variables */
    --theme-transition: all 0.3s var(--animation-smooth);
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --primary-color: #8B4513;
    --primary-light: #A0522D;
    --primary-dark: #654321;
    --secondary-color: #CD853F;
    --accent-color: #DEB887;
    --highlight-color: #FFB347;
    --highlight-light: #FFC966;
    --highlight-dark: #E6A033;

    --text-color: #F5F5F5;
    --text-light: #E0E0E0;
    --text-muted: #B0B0B0;
    --light-text: #2D2424;

    --background-color: #1A1A1A;
    --background-alt: #2D2D2D;
    --card-color: #2A2A2A;
    --card-hover: #333333;

    --border-color: #404040;
    --border-light: #353535;
    --border-dark: #555555;

    --glass-bg: rgba(42, 42, 42, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--highlight-color);
    }
    50% {
        box-shadow: 0 0 20px var(--highlight-color), 0 0 30px var(--highlight-color);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

/* Enhanced CSS - Compatible with existing styles */
/* Note: CSS reset is handled by style.css to avoid conflicts */

/* Enhanced body styles - only add new properties */
body.studynotes-enhanced {
    scroll-behavior: smooth;
    transition: var(--theme-transition);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enable custom cursor only when explicitly requested */
body.custom-cursor-enabled {
    cursor: none;
}

/* Custom Cursor System */
.custom-cursor {
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: var(--highlight-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: var(--z-cursor);
    transition: all 0.1s ease;
    transform: translate(-50%, -50%);
    mix-blend-mode: difference;
}

.custom-cursor::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 2px solid var(--highlight-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    transition: all 0.3s ease;
}

.custom-cursor.hover {
    transform: translate(-50%, -50%) scale(1.5);
    background: var(--primary-color);
}

.custom-cursor.hover::before {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.6;
}

.custom-cursor.click {
    transform: translate(-50%, -50%) scale(0.8);
}

.custom-cursor.text {
    width: 2px;
    height: 20px;
    border-radius: 1px;
    background: var(--text-color);
}

.custom-cursor.drag {
    transform: translate(-50%, -50%) scale(1.2);
    background: var(--success-color);
}

.custom-cursor.disabled {
    background: var(--danger-color);
    transform: translate(-50%, -50%) scale(0.8);
}

/* Cursor Trail Effect */
.cursor-trail {
    position: fixed;
    width: 6px;
    height: 6px;
    background: var(--highlight-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: calc(var(--z-cursor) - 1);
    opacity: 0.6;
    transition: all 0.2s ease;
}

/* Page Transition Effects */
.page-transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--highlight-color));
    z-index: 9998;
    transform: translateX(-100%);
    transition: transform 0.6s var(--animation-smooth);
}

.page-transition.active {
    transform: translateX(0);
}

.page-transition.exit {
    transform: translateX(100%);
}

/* Glassmorphism Effects */
.glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Advanced Loading States */
.skeleton {
    background: linear-gradient(90deg,
        var(--border-light) 25%,
        var(--border-color) 50%,
        var(--border-light) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

.skeleton-text {
    height: 1em;
    margin-bottom: 0.5em;
}

.skeleton-text:last-child {
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-button {
    height: 40px;
    width: 120px;
}

/* Pulse Loading Animation */
.pulse-loader {
    display: inline-block;
    width: 40px;
    height: 40px;
    position: relative;
}

.pulse-loader::before,
.pulse-loader::after {
    content: '';
    position: absolute;
    border: 4px solid var(--highlight-color);
    border-radius: 50%;
    opacity: 1;
    animation: pulse-ring 2s ease-in-out infinite;
}

.pulse-loader::after {
    animation-delay: 1s;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Focus Management for Accessibility */
:focus {
    outline: 2px solid var(--highlight-color);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 2px solid var(--highlight-color);
    outline-offset: 2px;
}

/* Improved Link Styles */
a {
    color: var(--highlight-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--highlight-dark);
    text-decoration: underline;
}

a:focus {
    color: var(--highlight-dark);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-md);
    color: var(--primary-color);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--space-md);
    line-height: var(--line-height-relaxed);
}

/* Enhanced Button System - Extends existing .btn from style.css */
.btn.enhanced {
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s var(--animation-smooth);
}

/* Enhanced button effects - only apply to buttons with specific classes */

.btn.enhanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    z-index: 0;
}

.btn.enhanced:hover::before {
    width: 300px;
    height: 300px;
}

.btn.enhanced > * {
    position: relative;
    z-index: 1;
}

.btn.enhanced:active {
    transform: scale(0.98);
}

.btn.ripple-effect {
    position: relative;
    overflow: hidden;
}

.btn.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    pointer-events: none;
}

.btn.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-text);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--light-text);
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    min-height: 2rem;
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-lg);
    min-height: 3rem;
}

/* Enhanced Card System - Only applies to cards with enhanced classes */
/* Note: Basic .card styles are handled by existing CSS files */

.card.enhanced {
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s var(--animation-smooth);
}

.card.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.card.enhanced:hover::before {
    left: 100%;
}

.card.enhanced:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-8px) scale(1.02);
    border-color: var(--highlight-color);
}

.card.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
}

.card.floating {
    animation: float 3s ease-in-out infinite;
}

.card.glow-effect:hover {
    animation: glow 2s ease-in-out infinite;
}

.card.tilt-effect {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.card.tilt-effect:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateY(-10px);
}

/* Interactive Card Variants */
.card-interactive {
    cursor: pointer;
    transition: all 0.3s var(--animation-elastic);
}

.card-interactive:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-interactive:active {
    transform: translateY(-5px) scale(1.02);
}

/* Magnetic Card Effect */
.card-magnetic {
    transition: transform 0.3s ease;
}

.card-magnetic:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

/* Parallax Card Effect */
.card-parallax {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.card-parallax .card-content {
    transform: translateZ(20px);
}

.card-parallax:hover {
    transform: perspective(1000px) rotateX(10deg);
}

.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--background-alt);
}

.card-body {
    padding: var(--space-lg);
}

.card-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-light);
    background-color: var(--background-alt);
}

/* Enhanced Form System */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.form-label.required::after {
    content: ' *';
    color: var(--danger-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-color);
    background-color: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    appearance: none;
}

.form-control:focus {
    border-color: var(--highlight-color);
    box-shadow: 0 0 0 3px rgba(232, 135, 30, 0.1);
    outline: none;
}

.form-control:disabled {
    background-color: var(--background-alt);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-invalid:focus {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-valid:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Form Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-xs);
    font-size: var(--font-size-sm);
    color: var(--danger-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-xs);
    font-size: var(--font-size-sm);
    color: var(--success-color);
}

.form-text {
    margin-top: var(--space-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Select Styling */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--space-sm) center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

/* Checkbox and Radio Styling */
.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: var(--space-sm);
}

.form-check-input {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    margin-left: -1.5em;
    vertical-align: top;
    background-color: var(--card-color);
    border: 1px solid var(--border-color);
    appearance: none;
    color-adjust: exact;
}

.form-check-input[type="checkbox"] {
    border-radius: var(--radius-sm);
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input:checked {
    background-color: var(--highlight-color);
    border-color: var(--highlight-color);
}

.form-check-input:checked[type="checkbox"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-label {
    color: var(--text-color);
    cursor: pointer;
}

/* Enhanced Layout System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--space-md);
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--space-sm));
}

.col {
    flex: 1;
    padding: 0 var(--space-sm);
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
    padding: 0 var(--space-sm);
}

/* Responsive Grid Columns */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Enhanced Modal System */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-modal);
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: var(--space-lg);
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: var(--card-color);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    outline: 0;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modal-title {
    margin: 0;
    line-height: var(--line-height-normal);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: var(--space-lg);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-sm);
    padding: var(--space-lg);
    border-top: 1px solid var(--border-light);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    margin: 0;
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-color);
}

/* Alert System */
.alert {
    position: relative;
    padding: var(--space-md);
    margin-bottom: var(--space-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

.alert-success {
    color: #155724;
    background-color: var(--success-light);
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: var(--danger-light);
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: var(--warning-light);
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: var(--info-light);
    border-color: #bee5eb;
}

/* Loading States */
.loading {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-muted);
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--highlight-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-xs) !important; }
.m-2 { margin: var(--space-sm) !important; }
.m-3 { margin: var(--space-md) !important; }
.m-4 { margin: var(--space-lg) !important; }
.m-5 { margin: var(--space-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-xs) !important; }
.p-2 { padding: var(--space-sm) !important; }
.p-3 { padding: var(--space-md) !important; }
.p-4 { padding: var(--space-lg) !important; }
.p-5 { padding: var(--space-xl) !important; }

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }

    .modal-dialog {
        max-width: 90%;
    }
}

@media (max-width: 992px) {
    .container {
        max-width: 720px;
    }

    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }

    .modal-dialog {
        max-width: 95%;
        margin: var(--space-sm);
    }

    .btn-lg {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 768px) {
    .container {
        max-width: 540px;
        padding: 0 var(--space-sm);
    }

    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }

    .row {
        margin: 0 calc(-1 * var(--space-xs));
    }

    .col, .col-auto, [class*="col-"] {
        padding: 0 var(--space-xs);
    }

    .modal-dialog {
        margin: var(--space-xs);
        max-width: calc(100% - 2 * var(--space-xs));
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--space-md);
    }

    .btn {
        min-height: 2.75rem;
        padding: var(--space-sm) var(--space-md);
    }

    .btn-sm {
        min-height: 2.25rem;
        padding: var(--space-xs) var(--space-sm);
    }

    .btn-lg {
        min-height: 3.25rem;
        padding: var(--space-md) var(--space-lg);
    }

    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
    h4 { font-size: var(--font-size-lg); }

    .card-header,
    .card-body,
    .card-footer {
        padding: var(--space-md);
    }
}

@media (max-width: 576px) {
    .container {
        max-width: 100%;
        padding: 0 var(--space-xs);
    }

    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }

    .modal-dialog {
        margin: 0;
        max-width: 100%;
        height: 100%;
    }

    .modal-content {
        height: 100%;
        border-radius: 0;
    }

    .btn {
        width: 100%;
        margin-bottom: var(--space-xs);
    }

    .btn:last-child {
        margin-bottom: 0;
    }

    .modal-footer {
        flex-direction: column-reverse;
    }

    .modal-footer .btn {
        margin-bottom: var(--space-xs);
    }

    .modal-footer .btn:first-child {
        margin-bottom: 0;
    }

    h1 { font-size: var(--font-size-2xl); }
    h2 { font-size: var(--font-size-xl); }
    h3 { font-size: var(--font-size-lg); }
    h4 { font-size: var(--font-size-base); }

    .card-header,
    .card-body,
    .card-footer {
        padding: var(--space-sm);
    }

    .form-group {
        margin-bottom: var(--space-md);
    }

    .alert {
        padding: var(--space-sm);
        font-size: var(--font-size-xs);
    }
}

/* Advanced Interactive Elements */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s var(--animation-smooth);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.parallax {
    will-change: transform;
}

.magnetic {
    transition: transform 0.3s var(--animation-elastic);
    cursor: pointer;
}

.morph-shape {
    transition: all 0.5s var(--animation-elastic);
    background: var(--highlight-color);
    width: 60px;
    height: 60px;
    border-radius: 10px;
    cursor: pointer;
}

/* Progress Components */
.progress-container {
    background: var(--border-light);
    border-radius: var(--radius-full);
    overflow: hidden;
    height: 8px;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--highlight-color), var(--highlight-light));
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 1.5s var(--animation-smooth);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Counter Animations */
.counter {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--highlight-color);
    display: inline-block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin: var(--space-xl) 0;
}

.stat-card {
    text-align: center;
    padding: var(--space-xl);
    background: var(--card-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    animation: fadeInUp 0.6s var(--animation-smooth);
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--highlight-color);
    margin-bottom: var(--space-sm);
}

.stat-label {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Interactive Dashboard Widgets */
.widget {
    background: var(--card-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--highlight-color));
}

.widget:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-md);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
    margin: 0;
}

.widget-icon {
    width: 24px;
    height: 24px;
    color: var(--highlight-color);
}

/* Advanced Search Component */
.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    padding-left: 50px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: var(--card-color);
}

.search-input:focus {
    border-color: var(--highlight-color);
    box-shadow: 0 0 0 4px rgba(232, 135, 30, 0.1);
    outline: none;
}

.search-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.search-input:focus + .search-icon {
    color: var(--highlight-color);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: var(--z-dropdown);
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    pointer-events: none;
}

.search-results.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.search-result-item {
    padding: var(--space-md);
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-result-item:hover {
    background: var(--background-alt);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Drag and Drop Styles */
.sortable-container {
    min-height: 100px;
    padding: var(--space-md);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.sortable-container.drag-over {
    border-color: var(--highlight-color);
    background: rgba(232, 135, 30, 0.05);
}

.sortable-item {
    background: var(--card-color);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    cursor: move;
    transition: all var(--transition-fast);
    user-select: none;
}

.sortable-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.sortable-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.sortable-item.drag-placeholder {
    background: var(--background-alt);
    border-style: dashed;
    opacity: 0.5;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-alt);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--highlight-color);
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--background-alt);
}

/* Image Effects */
.image-hover-zoom {
    overflow: hidden;
    border-radius: var(--radius-lg);
}

.image-hover-zoom img {
    transition: transform 0.5s var(--animation-smooth);
    width: 100%;
    height: auto;
}

.image-hover-zoom:hover img {
    transform: scale(1.1);
}

.image-blur-load {
    filter: blur(10px);
    transition: filter 0.5s ease;
}

.image-blur-load.loaded {
    filter: blur(0);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.content-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.content-card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
    line-height: 1.3;
}

.content-card-body {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    flex-grow: 1;
    overflow: hidden;
}

.content-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    margin-top: auto;
}

.content-card-date {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.content-card-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.content-card-actions {
    display: flex;
    gap: 5px;
}

/* Standardized Action Buttons */
.btn-sm {
    background-color: var(--accent-color);
    color: #000000 !important;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.btn-sm:hover {
    background-color: var(--primary-color);
    color: #ffffff !important;
    transform: translateY(-1px);
}

.btn-sm i {
    margin-right: 4px;
    font-size: 12px;
}

.btn-sm.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-sm.btn-danger:hover {
    background-color: #c82333;
    color: white;
}

/* Standardized Button System */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-primary:hover {
    background-color: var(--highlight-color);
    color: var(--light-text);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background-color: #f8f9fa;
    color: var(--text-color);
    border: 1px solid #dee2e6;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 120px;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.btn-nav {
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.btn-nav:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-1px);
}

.btn-nav i {
    margin-right: 6px;
    font-size: 12px;
}

/* Button sizing modifiers */
.btn-full-width {
    width: 100%;
}

.btn-large {
    padding: 16px 24px;
    font-size: 18px;
}

/* Standardized Profile Components */
.profile-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    margin: 20px 0;
}

.profile-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--accent-color);
    height: fit-content;
}

.profile-avatar {
    text-align: center;
    margin-bottom: 20px;
}

.profile-avatar i {
    font-size: 64px;
    color: var(--primary-color);
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.profile-info {
    text-align: center;
    margin-bottom: 25px;
}

.profile-info h3 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 22px;
}

.profile-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 14px;
}

.profile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.profile-stat-item {
    text-align: center;
    padding: 15px;
    background-color: rgba(101, 53, 15, 0.05);
    border-radius: 6px;
}

.profile-stat-item h4 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: bold;
}

.profile-stat-item p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Content View Cards (for individual pages) */
.content-view-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--accent-color);
    margin-bottom: 25px;
}

.content-view-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.content-view-title {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 28px;
    line-height: 1.3;
}

.content-view-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    color: var(--secondary-color);
    font-size: 14px;
}

.content-view-meta i {
    color: var(--highlight-color);
    margin-right: 5px;
}

.content-view-body {
    line-height: 1.8;
    color: var(--text-color);
    font-size: 16px;
}

.content-view-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Module-specific styling within content cards */
.module-stats {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.note-count {
    color: var(--secondary-color);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
}

.note-count i {
    margin-right: 5px;
    color: var(--highlight-color);
}

/* Legacy card styles for backward compatibility */
.card-header {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 18px;
    flex: 1;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 15px;
}

/* Grid Layouts */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Module Badge */
.module-badge {
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 10px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--accent-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Loading Spinner */
.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--highlight-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Generation Progress */
.generation-progress {
    text-align: center;
    padding: 30px 20px;
}

.generation-progress p {
    margin-bottom: 5px;
    color: var(--primary-color);
    font-weight: 500;
}

.generation-progress p.small {
    font-size: 14px;
    color: var(--secondary-color);
}

/* Message Styles */
.message {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.warning-message {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%);
    color: #8a6914;
    border: 1px solid #ffc107;
    border-left: 4px solid #ff9800;
    border-radius: 6px;
    padding: 16px 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);
    font-weight: 500;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: default;
}

.warning-message:hover {
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.25);
    transform: translateY(-1px);
}

.warning-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff9800, #ffc107, #ff9800);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.warning-message i {
    color: #ff9800;
    font-size: 18px;
    margin-right: 12px;
    vertical-align: middle;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.warning-message .warning-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.warning-message .warning-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.6;
}

.warning-message .warning-title {
    font-weight: 600;
    color: #8a6914;
    margin-bottom: 4px;
    font-size: 15px;
}

.warning-message .warning-subtitle {
    color: #a67c00;
    font-size: 13px;
    margin-top: 6px;
    font-style: italic;
}

/* Responsive design for warning messages */
@media (max-width: 768px) {
    .warning-message {
        padding: 14px 16px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .warning-message i {
        font-size: 16px;
        margin-right: 10px;
    }

    .warning-message .warning-content {
        gap: 10px;
    }

    .warning-message .warning-text {
        font-size: 13px;
    }

    .warning-message .warning-title {
        font-size: 14px;
    }

    .warning-message .warning-subtitle {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .warning-message {
        padding: 12px 14px;
        margin-bottom: 16px;
    }

    .warning-message .warning-content {
        flex-direction: column;
        gap: 8px;
    }

    .warning-message i {
        align-self: flex-start;
        margin-right: 0;
        margin-bottom: 4px;
    }
}

/* Dark mode support for warning messages */
@media (prefers-color-scheme: dark) {
    .warning-message {
        background: linear-gradient(135deg, #2d2416 0%, #3d3020 100%);
        color: #f4d03f;
        border-color: #f39c12;
        border-left-color: #e67e22;
        box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);
    }

    .warning-message:hover {
        box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
    }

    .warning-message i {
        color: #e67e22;
    }

    .warning-message .warning-title {
        color: #f4d03f;
    }

    .warning-message .warning-subtitle {
        color: #d4ac0d;
    }

    .warning-message::before {
        background: linear-gradient(90deg, #e67e22, #f39c12, #e67e22);
    }
}

/* Enhanced Difficulty Level Styling */
.difficulty-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.difficulty-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.difficulty-badge:hover::before {
    left: 100%;
}

.difficulty-badge i {
    font-size: 10px;
}

/* Easy Difficulty */
.difficulty-badge.easy {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-color: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.difficulty-badge.easy:hover {
    background: linear-gradient(135deg, #c3e6cb 0%, #b8dcc0 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transform: translateY(-1px);
}

/* Medium Difficulty */
.difficulty-badge.medium {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-color: #ffc107;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.difficulty-badge.medium:hover {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    transform: translateY(-1px);
}

/* Hard Difficulty */
.difficulty-badge.hard {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-color: #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.difficulty-badge.hard:hover {
    background: linear-gradient(135deg, #f5c6cb 0%, #f1b0b7 100%);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    transform: translateY(-1px);
}

/* Enhanced Radio Button Styling for Difficulty Selection */
.difficulty-radio-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 8px;
}

.difficulty-radio-item {
    position: relative;
    flex: 1;
    min-width: 120px;
}

.difficulty-radio-item input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.difficulty-radio-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: 2px solid;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
    position: relative;
    overflow: hidden;
}

.difficulty-radio-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.difficulty-radio-label:hover::before {
    left: 100%;
}

.difficulty-radio-label i {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.difficulty-radio-label:hover i {
    transform: scale(1.1);
}

/* Easy Radio Button */
.difficulty-radio-item.easy .difficulty-radio-label {
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    color: #155724;
    border-color: #28a745;
}

.difficulty-radio-item.easy input:checked + .difficulty-radio-label {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transform: translateY(-2px);
}

.difficulty-radio-item.easy .difficulty-radio-label:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

/* Medium Radio Button */
.difficulty-radio-item.medium .difficulty-radio-label {
    background: linear-gradient(135deg, #fffef7 0%, #fff8e1 100%);
    color: #856404;
    border-color: #ffc107;
}

.difficulty-radio-item.medium input:checked + .difficulty-radio-label {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    transform: translateY(-2px);
}

.difficulty-radio-item.medium .difficulty-radio-label:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

/* Hard Radio Button */
.difficulty-radio-item.hard .difficulty-radio-label {
    background: linear-gradient(135deg, #fefafa 0%, #fdf2f2 100%);
    color: #721c24;
    border-color: #dc3545;
}

.difficulty-radio-item.hard input:checked + .difficulty-radio-label {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    transform: translateY(-2px);
}

.difficulty-radio-item.hard .difficulty-radio-label:hover {
    background: linear-gradient(135deg, #fdf2f2 0%, #f8d7da 100%);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

/* Responsive Design for Difficulty Elements */
@media (max-width: 768px) {
    .difficulty-radio-group {
        flex-direction: column;
        gap: 8px;
    }

    .difficulty-radio-item {
        min-width: auto;
    }

    .difficulty-radio-label {
        padding: 10px 14px;
        font-size: 12px;
    }

    .difficulty-badge {
        font-size: 11px;
        padding: 4px 10px;
    }
}

@media (max-width: 480px) {
    .difficulty-radio-label {
        padding: 8px 12px;
        font-size: 11px;
        gap: 6px;
    }

    .difficulty-radio-label i {
        font-size: 12px;
    }

    .difficulty-badge {
        font-size: 10px;
        padding: 3px 8px;
        gap: 4px;
    }

    .difficulty-badge i {
        font-size: 8px;
    }
}

/* Dark Mode Support for Difficulty Elements */
@media (prefers-color-scheme: dark) {
    /* Easy Difficulty - Dark Mode */
    .difficulty-badge.easy {
        background: linear-gradient(135deg, #1e3a1e 0%, #2d5a2d 100%);
        color: #90ee90;
        border-color: #4caf50;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }

    .difficulty-badge.easy:hover {
        background: linear-gradient(135deg, #2d5a2d 0%, #3e6b3e 100%);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    .difficulty-radio-item.easy .difficulty-radio-label {
        background: linear-gradient(135deg, #1a2e1a 0%, #1e3a1e 100%);
        color: #90ee90;
        border-color: #4caf50;
    }

    .difficulty-radio-item.easy input:checked + .difficulty-radio-label {
        background: linear-gradient(135deg, #1e3a1e 0%, #2d5a2d 100%);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    .difficulty-radio-item.easy .difficulty-radio-label:hover {
        background: linear-gradient(135deg, #1e3a1e 0%, #2d5a2d 100%);
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }

    /* Medium Difficulty - Dark Mode */
    .difficulty-badge.medium {
        background: linear-gradient(135deg, #3d3020 0%, #4d4030 100%);
        color: #ffd700;
        border-color: #ff9800;
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    }

    .difficulty-badge.medium:hover {
        background: linear-gradient(135deg, #4d4030 0%, #5d5040 100%);
        box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
    }

    .difficulty-radio-item.medium .difficulty-radio-label {
        background: linear-gradient(135deg, #2d2416 0%, #3d3020 100%);
        color: #ffd700;
        border-color: #ff9800;
    }

    .difficulty-radio-item.medium input:checked + .difficulty-radio-label {
        background: linear-gradient(135deg, #3d3020 0%, #4d4030 100%);
        box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
    }

    .difficulty-radio-item.medium .difficulty-radio-label:hover {
        background: linear-gradient(135deg, #3d3020 0%, #4d4030 100%);
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    }

    /* Hard Difficulty - Dark Mode */
    .difficulty-badge.hard {
        background: linear-gradient(135deg, #3d1a1a 0%, #4d2020 100%);
        color: #ff6b6b;
        border-color: #f44336;
        box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    }

    .difficulty-badge.hard:hover {
        background: linear-gradient(135deg, #4d2020 0%, #5d2a2a 100%);
        box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    }

    .difficulty-radio-item.hard .difficulty-radio-label {
        background: linear-gradient(135deg, #2d1414 0%, #3d1a1a 100%);
        color: #ff6b6b;
        border-color: #f44336;
    }

    .difficulty-radio-item.hard input:checked + .difficulty-radio-label {
        background: linear-gradient(135deg, #3d1a1a 0%, #4d2020 100%);
        box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    }

    .difficulty-radio-item.hard .difficulty-radio-label:hover {
        background: linear-gradient(135deg, #3d1a1a 0%, #4d2020 100%);
        box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    }
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: var(--danger-color);
}

/* Filter Badge */
.filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 10px;
}

.filter-badge a {
    color: var(--primary-color);
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
}

.filter-badge a:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: 10px;
}

/* Relationship Card */
.relationship-card {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
}

.relationship-icon {
    font-size: 24px;
    color: var(--primary-color);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
}

.relationship-content {
    flex: 1;
}

.relationship-content h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.relationship-content p {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* Question/Content Expansion */
.expandable-content {
    max-height: 200px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.expandable-content.expanded {
    max-height: 800px;
}

.expand-collapse-btn {
    text-align: center;
    margin-top: 10px;
}

/* Option Items */
.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    transition: background-color 0.2s, border-color 0.2s;
}

.option-item:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.option-item input[type="radio"] {
    margin-right: 10px;
}

.option-item label {
    flex: 1;
    cursor: pointer;
    font-size: 16px;
    margin: 0;
}

/* Result Options */
.option-result {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.option-marker {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.option-text {
    flex: 1;
}

.correct-option {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 3px solid var(--success-color);
}

.incorrect-option {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 3px solid var(--danger-color);
}

/* Modern module card styling */
.module-card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.module-header {
    margin-bottom: var(--space-md);
}

.module-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.module-description {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
    flex-grow: 1;
}

.module-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 13px;
    color: var(--secondary-color);
}

.module-meta i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.module-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.05);
}

.btn-icon.delete-module-btn:hover {
    color: var(--danger-color);
}

/* Modern note card styling for dashboard compatibility */
.note-card {
    background: var(--card-color);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--highlight-color) 100%);
}

.note-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-md);
}

.note-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    flex: 1;
}

.note-preview {
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
    flex-grow: 1;
    overflow: hidden;
}

.note-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.note-date {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.note-date i {
    margin-right: 5px;
    color: var(--highlight-color);
}

.note-actions {
    display: flex;
    gap: 5px;
}

/* Grid layouts for legacy compatibility */
.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Standardized Feature Cards (Landing Page) */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin: 8px auto;
    max-width: 750px;
}

.feature-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 22px 17px;
    width: 264px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 5px solid var(--accent-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 42px;
    color: var(--highlight-color);
    margin-bottom: 10px;
    display: block;
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
}

.feature-card p {
    color: var(--secondary-color);
    line-height: 1.6;
    font-size: 15px;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin: 15px 0;
    padding: 0 20px;
}

.welcome-section h2 {
    color: var(--primary-color);
    font-size: 38px;
    margin-bottom: 8px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
    padding-bottom: 12px;
}

.welcome-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--highlight-color);
}

.welcome-section p {
    color: var(--secondary-color);
    font-size: 16px;
    line-height: 1.5;
    max-width: 650px;
    margin: 0 auto 8px auto;
    font-weight: 400;
}

/* Features Section (Reference Design) */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
    margin: 20px auto 8px auto;
    max-width: 850px;
}

/* Access Section (Reference Design) */
.access-section {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin: 12px auto;
    max-width: 650px;
}

.access-card {
    background-color: var(--card-color);
    border-radius: 10px;
    padding: 24px 20px;
    width: 286px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    border-bottom: 5px solid var(--highlight-color);
}

.access-card.admin {
    border-bottom-color: var(--primary-color);
}

.access-card:hover {
    transform: translateY(-5px);
}

.access-card h3 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
}

.access-card p {
    color: var(--secondary-color);
    margin-bottom: 15px;
    line-height: 1.4;
    font-size: 14px;
}

/* Reference Design Buttons */
.btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: var(--highlight-color);
    color: var(--light-text);
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--primary-color);
    color: #ffffff;
    transform: translateY(-3px);
    text-decoration: none;
}

.admin-btn {
    background-color: var(--primary-color);
}

.admin-btn:hover {
    background-color: var(--secondary-color);
}

/* Standardized Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background-color: var(--card-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(212, 163, 115, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    background-color: rgba(101, 53, 15, 0.1);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-info h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: bold;
}

.stat-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 14px;
}

/* Standardized Form Cards */
.form-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 20px;
}

.form-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 30px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-top: 4px solid var(--accent-color);
}

.form-card h2 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 25px;
    font-size: 24px;
}

.form-card h2 i {
    margin-right: 10px;
    color: var(--highlight-color);
}

/* Standardized Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(212, 163, 115, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

/* Responsive Design for All Components */
@media (max-width: 768px) {
    .grid-container, .notes-grid, .modules-grid, .feature-grid, .access-grid, .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .welcome-section {
        margin: 12px 0;
    }

    .welcome-section h2 {
        font-size: 32px;
        margin-bottom: 6px;
    }

    .welcome-section p {
        margin-bottom: 6px;
        font-size: 15px;
    }

    .features {
        margin: 15px auto 6px auto;
        gap: 20px;
    }

    .access-section {
        margin: 8px auto;
        gap: 25px;
    }

    .content-card, .note-card, .module-card, .feature-card, .access-card, .stat-card {
        padding: 15px;
    }

    .content-card-header h4, .note-header h4, .module-header h4, .feature-card h3, .access-card h3 {
        font-size: 16px;
    }

    .content-card-body, .note-preview, .module-description, .feature-card p, .access-card p {
        font-size: 13px;
    }

    .content-card-actions, .note-actions, .module-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .btn-sm, .btn-icon, .btn-nav {
        padding: 6px 10px;
        font-size: 12px;
    }

    .btn-primary, .btn-secondary {
        padding: 10px 16px;
        font-size: 14px;
        min-width: 100px;
    }

    .module-badge {
        font-size: 11px;
        padding: 2px 6px;
    }

    .profile-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .profile-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .form-card {
        padding: 20px;
        margin: 10px;
    }

    .content-view-card {
        padding: 20px;
    }

    .content-view-title {
        font-size: 24px;
    }

    .content-view-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .welcome-section {
        margin: 8px 0;
    }

    .welcome-section h2 {
        font-size: 28px;
        margin-bottom: 5px;
    }

    .welcome-section p {
        margin-bottom: 5px;
        font-size: 14px;
        line-height: 1.4;
    }

    .features {
        margin: 12px auto 5px auto;
        gap: 15px;
    }

    .access-section {
        margin: 6px auto;
        gap: 20px;
    }

    .feature-card {
        padding: 17px 13px;
        width: 100%;
        max-width: 308px;
    }

    .feature-card i {
        font-size: 36px;
        margin-bottom: 8px;
    }

    .feature-card h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }

    .access-card {
        padding: 20px 17px;
        width: 100%;
        max-width: 315px;
    }

    .access-card h3 {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .access-card p {
        font-size: 13px;
        margin-bottom: 12px;
    }

    .content-card-header, .note-header, .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .content-card-footer, .note-footer, .module-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .content-card-actions, .note-actions, .module-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
