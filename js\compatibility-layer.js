/**
 * StudyNotes Compatibility Layer
 * Ensures advanced features work seamlessly with existing codebase
 */

// Compatibility and debugging utilities
window.StudyNotesDebug = {
    // Check if advanced features are working
    checkFeatures() {
        const results = {
            cssLoaded: false,
            jsLoaded: false,
            advancedFeaturesEnabled: false,
            customCursorWorking: false,
            animationsWorking: false,
            themeSystemWorking: false,
            errors: []
        };

        try {
            // Check if CSS is loaded
            const testElement = document.createElement('div');
            testElement.className = 'btn enhanced';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            results.cssLoaded = styles.position === 'relative';
            document.body.removeChild(testElement);

            // Check if JavaScript is loaded
            results.jsLoaded = typeof StudyNotes !== 'undefined';

            // Check if advanced features are enabled
            results.advancedFeaturesEnabled = StudyNotes.config && StudyNotes.config.enableAdvancedFeatures;

            // Check custom cursor
            results.customCursorWorking = document.querySelector('.custom-cursor') !== null;

            // Check animations
            const animationElement = document.createElement('div');
            animationElement.className = 'animate-on-scroll';
            document.body.appendChild(animationElement);
            const animationStyles = window.getComputedStyle(animationElement);
            results.animationsWorking = animationStyles.opacity === '0';
            document.body.removeChild(animationElement);

            // Check theme system
            results.themeSystemWorking = document.querySelector('.theme-toggle') !== null;

        } catch (error) {
            results.errors.push(error.message);
        }

        return results;
    },

    // Display debug information
    showDebugInfo() {
        const results = this.checkFeatures();
        console.group('StudyNotes Debug Information');
        console.log('CSS Loaded:', results.cssLoaded ? '✅' : '❌');
        console.log('JavaScript Loaded:', results.jsLoaded ? '✅' : '❌');
        console.log('Advanced Features Enabled:', results.advancedFeaturesEnabled ? '✅' : '❌');
        console.log('Custom Cursor Working:', results.customCursorWorking ? '✅' : '❌');
        console.log('Animations Working:', results.animationsWorking ? '✅' : '❌');
        console.log('Theme System Working:', results.themeSystemWorking ? '✅' : '❌');
        
        if (results.errors.length > 0) {
            console.error('Errors found:', results.errors);
        }
        
        console.groupEnd();
        return results;
    },

    // Fix common issues
    fixCommonIssues() {
        const fixes = [];

        try {
            // Fix 1: Ensure StudyNotes is initialized
            if (typeof StudyNotes !== 'undefined' && !StudyNotes.state.initialized) {
                StudyNotes.init();
                fixes.push('Initialized StudyNotes');
            }

            // Fix 2: Remove conflicting styles
            const conflictingStyles = document.querySelectorAll('style[data-conflict]');
            conflictingStyles.forEach(style => {
                style.remove();
                fixes.push('Removed conflicting style');
            });

            // Fix 3: Ensure CSS variables are available
            if (!getComputedStyle(document.documentElement).getPropertyValue('--primary-color')) {
                const style = document.createElement('style');
                style.textContent = `
                    :root {
                        --primary-color: #65350F;
                        --secondary-color: #A67B5B;
                        --accent-color: #D4A373;
                        --highlight-color: #E8871E;
                        --text-color: #2D2424;
                        --light-text: #F5EBE0;
                        --background-color: #F5EBE0;
                        --card-color: #FFFFFF;
                    }
                `;
                document.head.appendChild(style);
                fixes.push('Added missing CSS variables');
            }

            // Fix 4: Enable advanced features if requested
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('advanced') === 'true') {
                this.enableAdvancedFeatures();
                fixes.push('Enabled advanced features via URL parameter');
            }

        } catch (error) {
            console.error('Error applying fixes:', error);
        }

        if (fixes.length > 0) {
            console.log('Applied fixes:', fixes);
        }

        return fixes;
    },

    // Enable advanced features safely
    enableAdvancedFeatures() {
        try {
            if (typeof StudyNotes !== 'undefined') {
                StudyNotes.enableAdvancedFeatures();
                
                // Add visual indicator
                const indicator = document.createElement('div');
                indicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    left: 10px;
                    background: #28a745;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 10000;
                    font-family: monospace;
                `;
                indicator.textContent = 'Advanced Features Enabled';
                document.body.appendChild(indicator);
                
                setTimeout(() => {
                    indicator.remove();
                }, 3000);
                
                return true;
            }
        } catch (error) {
            console.error('Error enabling advanced features:', error);
        }
        return false;
    },

    // Disable advanced features
    disableAdvancedFeatures() {
        try {
            if (typeof StudyNotes !== 'undefined') {
                StudyNotes.config.enableAdvancedFeatures = false;
                document.body.classList.remove('custom-cursor-enabled');
                
                // Remove custom cursor
                const cursor = document.querySelector('.custom-cursor');
                if (cursor) cursor.remove();
                
                // Remove theme toggle
                const themeToggle = document.querySelector('.theme-toggle');
                if (themeToggle) themeToggle.remove();
                
                console.log('Advanced features disabled');
                return true;
            }
        } catch (error) {
            console.error('Error disabling advanced features:', error);
        }
        return false;
    }
};

// Auto-fix on load
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure all scripts are loaded
    setTimeout(() => {
        StudyNotesDebug.fixCommonIssues();
        
        // Show debug info in console if debug mode is enabled
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('debug') === 'true') {
            StudyNotesDebug.showDebugInfo();
        }
    }, 100);
});

// Global functions for easy access
window.enableAdvancedFeatures = () => StudyNotesDebug.enableAdvancedFeatures();
window.disableAdvancedFeatures = () => StudyNotesDebug.disableAdvancedFeatures();
window.debugStudyNotes = () => StudyNotesDebug.showDebugInfo();

// Legacy function compatibility
if (typeof setupModals === 'undefined') {
    window.setupModals = function(config) {
        console.warn('setupModals called - using compatibility mode');
        if (typeof StudyNotes !== 'undefined' && StudyNotes.components && StudyNotes.components.modal) {
            // Use new modal system
            StudyNotes.components.modal.init();
        }
    };
}

if (typeof openModal === 'undefined') {
    window.openModal = function(modal) {
        console.warn('openModal called - using compatibility mode');
        if (typeof StudyNotes !== 'undefined' && StudyNotes.components && StudyNotes.components.modal) {
            const modalId = typeof modal === 'string' ? modal : '#' + modal.id;
            StudyNotes.components.modal.open(modalId);
        }
    };
}

if (typeof closeModal === 'undefined') {
    window.closeModal = function(modal) {
        console.warn('closeModal called - using compatibility mode');
        if (typeof StudyNotes !== 'undefined' && StudyNotes.components && StudyNotes.components.modal) {
            StudyNotes.components.modal.close(modal);
        }
    };
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StudyNotesDebug;
}
