<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyNotes Advanced Features Demo</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .demo-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--highlight-color));
            color: white;
            padding: var(--space-3xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .demo-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s linear infinite;
        }
        
        .demo-section {
            padding: var(--space-3xl) 0;
            position: relative;
        }
        
        .demo-section:nth-child(even) {
            background: var(--background-alt);
        }
        
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-xl);
            margin-top: var(--space-xl);
        }
        
        .interactive-demo {
            background: var(--card-color);
            padding: var(--space-xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }
        
        .demo-controls {
            display: flex;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
            flex-wrap: wrap;
        }
        
        .chart-container {
            height: 200px;
            margin: var(--space-lg) 0;
        }
        
        .progress-demo {
            margin: var(--space-lg) 0;
        }
        
        .cursor-demo-area {
            background: var(--background-alt);
            padding: var(--space-xl);
            border-radius: var(--radius-lg);
            text-align: center;
            margin: var(--space-lg) 0;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: var(--space-md);
        }
        
        .floating-elements {
            position: relative;
            height: 300px;
            background: linear-gradient(45deg, var(--background-color), var(--background-alt));
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .floating-shape {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--highlight-color);
            opacity: 0.7;
        }
        
        .floating-shape:nth-child(1) { top: 20%; left: 10%; animation: float 3s ease-in-out infinite; }
        .floating-shape:nth-child(2) { top: 60%; left: 70%; animation: float 4s ease-in-out infinite 1s; }
        .floating-shape:nth-child(3) { top: 30%; left: 50%; animation: float 5s ease-in-out infinite 2s; }
        
        .theme-demo {
            text-align: center;
            padding: var(--space-xl);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="demo-hero">
        <div class="container">
            <h1 class="animate-on-scroll">StudyNotes Advanced Features</h1>
            <p class="animate-on-scroll" style="animation-delay: 0.2s;">Experience the next generation of interactive web design</p>
            <div class="animate-on-scroll" style="animation-delay: 0.4s;">
                <button class="btn btn-primary btn-lg ripple-effect magnetic" onclick="StudyNotes.animations.createParticles(this)">
                    <i class="fas fa-rocket"></i>
                    Explore Features
                </button>
            </div>
        </div>
    </section>

    <!-- Custom Cursor Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Custom Cursor & Mouse Interactions</h2>
            <div class="cursor-demo-area">
                <h3>Move your mouse around this area</h3>
                <p>Notice the custom cursor that changes based on different elements</p>
                <div class="demo-controls">
                    <button class="btn btn-primary magnetic">Hover Me</button>
                    <input type="text" class="form-control" placeholder="Text cursor here" style="max-width: 200px;">
                    <div class="morph-shape magnetic"></div>
                </div>
                <p class="text-muted">The cursor has trail effects and changes appearance contextually</p>
            </div>
        </div>
    </section>

    <!-- Theme Toggle Demo -->
    <section class="demo-section">
        <div class="container">
            <div class="theme-demo">
                <h2 class="animate-on-scroll">Dark/Light Theme Toggle</h2>
                <p class="animate-on-scroll">Click the theme toggle button in the top-right corner to switch themes</p>
                <div class="card glass-effect" style="max-width: 400px; margin: 0 auto;">
                    <div class="card-body">
                        <h4>Glassmorphism Effect</h4>
                        <p>This card demonstrates the glassmorphism effect that adapts to both themes</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced Animations Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Advanced Animations & Effects</h2>
            <div class="feature-showcase">
                <!-- Stats Cards with Counters -->
                <div class="interactive-demo">
                    <h3>Animated Counters</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number counter" data-target="1250" data-duration="2000">0</div>
                            <div class="stat-label">Students</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number counter" data-target="89" data-duration="1500">0</div>
                            <div class="stat-label">Courses</div>
                        </div>
                    </div>
                </div>

                <!-- Progress Animations -->
                <div class="interactive-demo">
                    <h3>Progress Animations</h3>
                    <div class="progress-demo">
                        <label>JavaScript Skills</label>
                        <div class="progress-container">
                            <div class="progress-bar" data-progress="85"></div>
                        </div>
                    </div>
                    <div class="progress-demo">
                        <label>CSS Mastery</label>
                        <div class="progress-container">
                            <div class="progress-bar" data-progress="92"></div>
                        </div>
                    </div>
                    <div id="progressRing" class="chart-container"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Cards Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Interactive Card Effects</h2>
            <div class="feature-showcase">
                <div class="card card-interactive tilt-effect">
                    <div class="card-body">
                        <h4>3D Tilt Effect</h4>
                        <p>Hover over this card to see the 3D tilt effect in action</p>
                    </div>
                </div>
                
                <div class="card card-magnetic glow-effect">
                    <div class="card-body">
                        <h4>Magnetic & Glow</h4>
                        <p>This card has magnetic hover effects and glowing animation</p>
                    </div>
                </div>
                
                <div class="card floating glass-effect">
                    <div class="card-body">
                        <h4>Floating Glass</h4>
                        <p>Combines floating animation with glassmorphism effect</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Data Visualization Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Data Visualization</h2>
            <div class="feature-showcase">
                <div class="interactive-demo">
                    <h3>Animated Bar Chart</h3>
                    <div id="barChart" class="chart-container"></div>
                    <button class="btn btn-secondary" onclick="regenerateChart()">Regenerate Data</button>
                </div>
                
                <div class="interactive-demo">
                    <h3>Dashboard Widgets</h3>
                    <div class="widget">
                        <div class="widget-header">
                            <h4 class="widget-title">Study Progress</h4>
                            <i class="fas fa-chart-line widget-icon"></i>
                        </div>
                        <div class="widget-body">
                            <div class="counter" data-target="78" data-duration="1000">0</div>% Complete
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced Search Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Advanced Search</h2>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search for courses, notes, or topics..." id="advancedSearch">
                <i class="fas fa-search search-icon"></i>
                <div class="search-results" id="searchResults">
                    <div class="search-result-item">JavaScript Fundamentals</div>
                    <div class="search-result-item">CSS Grid Layout</div>
                    <div class="search-result-item">React Components</div>
                    <div class="search-result-item">Node.js Backend</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Drag and Drop Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Drag & Drop Interface</h2>
            <div class="sortable-container" id="sortableDemo">
                <div class="sortable-item" draggable="true">📚 Introduction to Programming</div>
                <div class="sortable-item" draggable="true">🎨 Web Design Basics</div>
                <div class="sortable-item" draggable="true">⚡ JavaScript Advanced</div>
                <div class="sortable-item" draggable="true">🚀 React Development</div>
            </div>
            <p class="text-center text-muted">Drag and drop the items above to reorder them</p>
        </div>
    </section>

    <!-- Floating Elements Demo -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center animate-on-scroll">Floating Elements & Parallax</h2>
            <div class="floating-elements parallax">
                <div class="floating-shape"></div>
                <div class="floating-shape"></div>
                <div class="floating-shape"></div>
            </div>
        </div>
    </section>

    <script src="js/common.js"></script>
    <script>
        // Demo-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize charts
            setTimeout(() => {
                StudyNotes.charts.createProgressRing(
                    document.getElementById('progressRing'), 
                    75, 
                    { size: 150, color: 'var(--success-color)' }
                );
                
                generateBarChart();
            }, 1000);
            
            // Advanced search demo
            const searchInput = document.getElementById('advancedSearch');
            const searchResults = document.getElementById('searchResults');
            
            searchInput.addEventListener('focus', () => {
                searchResults.classList.add('show');
            });
            
            searchInput.addEventListener('blur', () => {
                setTimeout(() => searchResults.classList.remove('show'), 200);
            });
            
            // Drag and drop demo
            setupDragAndDrop();
        });
        
        function generateBarChart() {
            const data = [
                { label: 'HTML', value: Math.floor(Math.random() * 100) + 50 },
                { label: 'CSS', value: Math.floor(Math.random() * 100) + 50 },
                { label: 'JS', value: Math.floor(Math.random() * 100) + 50 },
                { label: 'React', value: Math.floor(Math.random() * 100) + 50 }
            ];
            
            StudyNotes.charts.createBarChart(document.getElementById('barChart'), data);
        }
        
        function regenerateChart() {
            generateBarChart();
        }
        
        function setupDragAndDrop() {
            const container = document.getElementById('sortableDemo');
            let draggedElement = null;
            
            container.addEventListener('dragstart', (e) => {
                draggedElement = e.target;
                e.target.classList.add('dragging');
            });
            
            container.addEventListener('dragend', (e) => {
                e.target.classList.remove('dragging');
                draggedElement = null;
            });
            
            container.addEventListener('dragover', (e) => {
                e.preventDefault();
                const afterElement = getDragAfterElement(container, e.clientY);
                if (afterElement == null) {
                    container.appendChild(draggedElement);
                } else {
                    container.insertBefore(draggedElement, afterElement);
                }
            });
        }
        
        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }
    </script>
</body>
</html>
